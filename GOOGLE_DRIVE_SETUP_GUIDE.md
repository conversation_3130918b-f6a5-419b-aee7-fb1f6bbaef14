# 🌐 دليل إعداد Google Drive للنسور الماسية

## 🎯 نظرة عامة

بدلاً من Firebase، يمكن استخدام **Google Drive** كنظام مزامنة سحابية للتطبيق مع المزايا التالية:

### ✅ **المزايا:**
- **مجاني تماماً** - 15 جيجا مساحة مجانية
- **سهل الإعداد** - لا يحتاج خبرة تقنية
- **مألوف للجميع** - الكل يعرف Google Drive
- **آمن ومحمي** - بحماية Google
- **مشاركة سهلة** - بين الأجهزة والأشخاص
- **نسخ احتياطية تلقائية** - في السحابة

### ⚡ **كيف يعمل:**
- يحفظ ملفات JSON في مجلد خاص في Google Drive
- يرفع وينزل البيانات تلقائياً
- يحتفظ بنسخ مؤرخة من البيانات
- يعمل مع أي حساب Google

## 🛠️ خطوات الإعداد

### الخطوة 1: إنشاء مشروع في Google Cloud Console

1. **اذهب إلى Google Cloud Console:**
   - [console.cloud.google.com](https://console.cloud.google.com)

2. **إنشاء مشروع جديد:**
   ```
   - اضغط "Select a project" → "New Project"
   - اسم المشروع: Diamond Eagles Inventory
   - اضغط "Create"
   ```

3. **تفعيل Google Drive API:**
   ```
   - اذهب إلى "APIs & Services" → "Library"
   - ابحث عن "Google Drive API"
   - اضغط عليه ثم "Enable"
   ```

### الخطوة 2: إنشاء بيانات الاعتماد

1. **إنشاء OAuth 2.0 Client:**
   ```
   - اذهب إلى "APIs & Services" → "Credentials"
   - اضغط "Create Credentials" → "OAuth client ID"
   - اختر "Web application"
   ```

2. **تكوين OAuth:**
   ```
   Name: Diamond Eagles Web App
   
   Authorized JavaScript origins:
   - http://localhost:8000
   - file://
   
   Authorized redirect URIs:
   - http://localhost:8000
   ```

3. **نسخ البيانات:**
   ```
   ستحصل على:
   - Client ID: مثل 123456789-abc.apps.googleusercontent.com
   - Client Secret: (لن نحتاجه للويب)
   ```

### الخطوة 3: الحصول على API Key

1. **إنشاء API Key:**
   ```
   - في نفس صفحة "Credentials"
   - اضغط "Create Credentials" → "API key"
   - انسخ المفتاح
   ```

2. **تقييد API Key (اختياري):**
   ```
   - اضغط على المفتاح لتعديله
   - في "API restrictions" اختر "Google Drive API"
   - احفظ
   ```

### الخطوة 4: تحديث التكوين

في ملف `google-drive-sync.js` استبدل:

```javascript
const GOOGLE_DRIVE_CONFIG = {
    CLIENT_ID: 'YOUR_CLIENT_ID_HERE.apps.googleusercontent.com',
    API_KEY: 'YOUR_API_KEY_HERE',
    DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
    SCOPES: 'https://www.googleapis.com/auth/drive.file'
};
```

بالقيم الحقيقية من Google Cloud Console.

## 🚀 كيفية الاستخدام

### 1. **تسجيل الدخول:**
- افتح التطبيق في المتصفح
- اذهب إلى الإعدادات
- اضغط "تسجيل دخول Google"
- اختر حساب Google الخاص بك
- اقبل الأذونات

### 2. **رفع البيانات:**
- اضغط "رفع للسحابة" في قسم المنتجات
- اضغط "رفع للسحابة" في قسم العملاء
- أو استخدم "مزامنة شاملة"

### 3. **تحميل البيانات:**
- اضغط "تحميل من السحابة" في أي قسم
- ستحمل أحدث نسخة من Google Drive

### 4. **مشاركة البيانات:**
- شارك مجلد "النسور الماسية" في Google Drive
- مع أعضاء الفريق
- سيتمكنون من الوصول لنفس البيانات

## 📁 هيكل الملفات في Google Drive

سيتم إنشاء مجلد "النسور الماسية" يحتوي على:

```
📁 النسور الماسية/
├── 📄 products_2023-12-15.json
├── 📄 products_2023-12-14.json
├── 📄 customers_2023-12-15.json
├── 📄 customers_2023-12-14.json
└── 📄 backup_2023-12-15.json
```

## 🔒 الأمان والخصوصية

### ✅ **الحماية:**
- **OAuth 2.0** - مصادقة آمنة
- **أذونات محدودة** - الوصول للملفات المُنشأة فقط
- **تشفير Google** - حماية البيانات في النقل والتخزين
- **تحكم كامل** - يمكنك إلغاء الوصول في أي وقت

### 🛡️ **الخصوصية:**
- لا يتم الوصول لملفاتك الشخصية
- فقط مجلد التطبيق
- يمكنك حذف البيانات في أي وقت

## 📊 مقارنة مع Firebase

| الميزة | Google Drive | Firebase |
|--------|-------------|----------|
| **التكلفة** | مجاني (15 جيجا) | مجاني محدود |
| **سهولة الإعداد** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **المألوفية** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **المشاركة** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **السرعة** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **الميزات المتقدمة** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🎯 الخلاصة

**Google Drive مثالي إذا كنت تريد:**
- ✅ حل بسيط وسريع
- ✅ لا تريد تعقيدات تقنية
- ✅ مشاركة سهلة مع الفريق
- ✅ نسخ احتياطية مرئية
- ✅ تحكم كامل في البيانات

**Firebase أفضل إذا كنت تريد:**
- ⚡ أداء أسرع
- 🔄 مزامنة فورية
- 📊 ميزات متقدمة
- 🔍 استعلامات معقدة

---

**🌟 التوصية: ابدأ بـ Google Drive للبساطة، ويمكنك التطوير لـ Firebase لاحقاً!**
