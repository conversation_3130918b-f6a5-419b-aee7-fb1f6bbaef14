# 🔗 دليل إعداد Google Drive السريع

## 🎯 الهدف
ربط تطبيق النسور الماسية بـ Google Drive لمشاركة البيانات بين جميع المستخدمين

---

## ⚡ الطريقة السريعة (5 دقائق)

### الخطوة 1: إنشاء مشروع Google Cloud

1. **اذهب إلى:** [Google Cloud Console](https://console.cloud.google.com)

2. **إنشاء مشروع جديد:**
   ```
   - اضغط "Select a project" → "NEW PROJECT"
   - اسم المشروع: Diamond Eagles Inventory
   - اضغط "CREATE"
   ```

### الخطوة 2: تفعيل Google Drive API

1. **في القائمة الجانبية:**
   ```
   APIs & Services → Library
   ```

2. **ابحث عن:**
   ```
   "Google Drive API"
   ```

3. **اضغط عليه ثم:**
   ```
   "ENABLE"
   ```

### الخطوة 3: إعداد OAuth Consent Screen

1. **اذهب إلى:**
   ```
   APIs & Services → OAuth consent screen
   ```

2. **املأ البيانات:**
   ```
   - User Type: External
   - App name: Diamond Eagles Inventory
   - User support email: بريدك الإلكتروني
   - Developer contact: بريدك الإلكتروني
   ```

3. **اضغط:**
   ```
   "SAVE AND CONTINUE" في كل خطوة
   ```

### الخطوة 4: إنشاء OAuth Client ID

1. **اذهب إلى:**
   ```
   APIs & Services → Credentials
   ```

2. **اضغط:**
   ```
   "CREATE CREDENTIALS" → "OAuth client ID"
   ```

3. **املأ البيانات:**
   ```
   - Application type: Web application
   - Name: Diamond Eagles Web App
   ```

4. **في Authorized JavaScript origins أضف:**
   ```
   https://your-netlify-url.netlify.app
   
   مثال:
   https://singular-piroshki-802a71.netlify.app
   ```

5. **اضغط:**
   ```
   "CREATE"
   ```

### الخطوة 5: نسخ Client ID

1. **بعد الإنشاء ستحصل على:**
   ```
   Client ID: 123456789-abcdefghijk.apps.googleusercontent.com
   ```

2. **انسخ Client ID فقط**

### الخطوة 6: تحديث التطبيق

1. **في ملف `google-drive-sync.js` السطر 9:**
   ```javascript
   CLIENT_ID: 'ضع_Client_ID_هنا.apps.googleusercontent.com',
   ```

2. **أو في ملف `simple-google-drive.js` السطر 8:**
   ```javascript
   CLIENT_ID: 'ضع_Client_ID_هنا.apps.googleusercontent.com',
   ```

---

## 🚀 اختبار الاتصال

### بعد التحديث:

1. **ارفع الملفات المحدثة على Netlify**

2. **افتح التطبيق واذهب إلى الإعدادات**

3. **في قسم Google Drive اضغط:**
   ```
   "تسجيل دخول Google"
   ```

4. **إذا نجح الاتصال ستظهر:**
   ```
   ✅ حالة الاتصال: متصل
   ✅ المستخدم: اسمك
   ```

---

## 🔧 حل المشاكل الشائعة

### إذا ظهرت رسالة "This app isn't verified":
```
1. اضغط "Advanced"
2. اضغط "Go to Diamond Eagles Inventory (unsafe)"
3. هذا طبيعي للتطبيقات الجديدة
```

### إذا لم يعمل الاتصال:
```
1. تأكد من إضافة رابط Netlify في Authorized JavaScript origins
2. تأكد من نسخ Client ID بشكل صحيح
3. جرب إعادة تحميل الصفحة
```

### إذا ظهرت رسالة "Access blocked":
```
1. تأكد من إكمال OAuth consent screen
2. أضف بريدك الإلكتروني في Test users
```

---

## 📱 كيفية الاستخدام بعد الإعداد

### للمستخدم الأول:
```
1. سجل دخول Google Drive
2. أضف البيانات (منتجات، عملاء)
3. اضغط "رفع جميع البيانات"
```

### للمستخدمين الآخرين:
```
1. سجلوا دخول Google Drive (نفس الحساب أو حساب مشارك)
2. اضغطوا "تحميل جميع البيانات"
3. ستظهر جميع البيانات
```

### المزامنة التلقائية:
```
✅ تفعل تلقائياً بعد تسجيل الدخول
✅ تحديث كل 30 ثانية
✅ رفع فوري عند إضافة بيانات جديدة
```

---

## 🎊 النتيجة النهائية

بعد الإعداد ستحصل على:

- ✅ **مشاركة فورية** للبيانات بين جميع المستخدمين
- ✅ **مزامنة تلقائية** كل 30 ثانية
- ✅ **نسخ احتياطية** آمنة في Google Drive
- ✅ **وصول من أي مكان** في العالم
- ✅ **عمل جماعي** على نفس البيانات

---

## 📞 المساعدة

إذا واجهت أي مشكلة:

1. **تأكد من اتباع الخطوات بالترتيب**
2. **تحقق من رابط Netlify في Google Cloud Console**
3. **جرب إعادة تحميل الصفحة**
4. **تأكد من تفعيل Google Drive API**

**🌟 بعد الإعداد ستعمل المزامنة تلقائياً وستظهر البيانات لجميع المستخدمين فوراً!**
