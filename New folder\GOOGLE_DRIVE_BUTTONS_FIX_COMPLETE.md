# 🔧 حل مشكلة عدم عمل أزرار Google Drive

## ❌ **المشكلة:**
```
أز<PERSON><PERSON>ر Google Drive لا تعمل:
- تسجيل دخول Google ❌
- رفع جميع البيانات ❌  
- تحميل جميع البيانات ❌
- فحص الاتصال ❌
```

## 🔍 **الأسباب الجذرية:**

### **1. Google API غير محمل** 📡
- **لا يوجد script tag** لـ Google APIs في HTML
- **تحميل متأخر** للـ API
- **عدم انتظار تحميل gapi** بشكل صحيح

### **2. وظيفة التهيئة مفقودة** ⚙️
- **`initializeGoogleDriveSync` غير موجودة**
- **عدم تهيئة Google Client** بشكل صحيح
- **عدم ربط الأزرار بالوظائف**

### **3. ترتيب التحميل خاطئ** 🔄
- **Google API يحمل بعد الحاجة إليه**
- **عدم انتظار DOM** قبل التهيئة
- **عدم وجود fallback** للتهيئة

## ✅ **الحلول المطبقة:**

### **1. إضافة Google API في HTML** 📡
```html
<!-- Google APIs -->
<script src="https://apis.google.com/js/api.js"></script>
```

**الموقع:** قبل تحميل ملفات JavaScript الأخرى

### **2. تحسين تحميل Google API** ⚡
```javascript
function loadGoogleAPI() {
    return new Promise((resolve, reject) => {
        console.log('📥 تحميل Google API...');

        if (window.gapi) {
            console.log('✅ Google API محمل مسبقاً');
            gapi = window.gapi;
            resolve();
            return;
        }

        // Check if script already exists
        const existingScript = document.querySelector('script[src*="apis.google.com"]');
        if (existingScript) {
            console.log('⏳ Google API قيد التحميل...');
            
            // If already loaded
            if (window.gapi) {
                gapi = window.gapi;
                console.log('✅ Google API محمل مسبقاً');
                resolve();
                return;
            }
            
            existingScript.onload = () => {
                gapi = window.gapi;
                console.log('✅ تم تحميل Google API');
                resolve();
            };
            
            // Fallback: check periodically if gapi becomes available
            let checkCount = 0;
            const checkInterval = setInterval(() => {
                checkCount++;
                if (window.gapi) {
                    clearInterval(checkInterval);
                    gapi = window.gapi;
                    console.log('✅ تم تحميل Google API (عبر الفحص الدوري)');
                    resolve();
                } else if (checkCount > 20) { // 10 seconds timeout
                    clearInterval(checkInterval);
                    console.error('❌ انتهت مهلة انتظار تحميل Google API');
                    reject(new Error('انتهت مهلة انتظار تحميل Google API'));
                }
            }, 500);
            return;
        }

        // Create script if doesn't exist
        console.log('🔄 إنشاء script tag لـ Google API...');
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.async = true;
        script.defer = true;

        script.onload = () => {
            console.log('📥 تم تحميل ملف Google API');
            
            // Wait for gapi to be available
            let waitCount = 0;
            const waitForGapi = setInterval(() => {
                waitCount++;
                if (window.gapi) {
                    clearInterval(waitForGapi);
                    gapi = window.gapi;
                    console.log('✅ تم تحميل Google API بنجاح');
                    resolve();
                } else if (waitCount > 20) { // 10 seconds timeout
                    clearInterval(waitForGapi);
                    console.error('❌ انتهت مهلة انتظار تحميل gapi');
                    reject(new Error('انتهت مهلة انتظار تحميل gapi'));
                }
            }, 500);
        };

        script.onerror = (error) => {
            console.error('❌ فشل في تحميل Google API:', error);
            reject(new Error('فشل في تحميل Google API'));
        };

        document.head.appendChild(script);
        console.log('📤 تم إضافة script tag إلى الصفحة');
    });
}
```

### **3. إضافة وظيفة التهيئة الكاملة** ⚙️
```javascript
async function initializeGoogleDriveSync() {
    console.log('🚀 تهيئة نظام Google Drive...');
    
    try {
        // Load Google API first
        await loadGoogleAPI();
        console.log('✅ تم تحميل Google API');
        
        // Initialize Google API
        await new Promise((resolve, reject) => {
            gapi.load('auth2:client', {
                callback: resolve,
                onerror: reject
            });
        });
        console.log('✅ تم تحميل مكونات Google API');
        
        // Initialize client
        await gapi.client.init({
            apiKey: GOOGLE_DRIVE_CONFIG.API_KEY,
            clientId: GOOGLE_DRIVE_CONFIG.CLIENT_ID,
            discoveryDocs: [GOOGLE_DRIVE_CONFIG.DISCOVERY_DOC],
            scope: GOOGLE_DRIVE_CONFIG.SCOPES
        });
        console.log('✅ تم تهيئة Google Client');
        
        // Check if user is already signed in
        const authInstance = gapi.auth2.getAuthInstance();
        if (authInstance.isSignedIn.get()) {
            googleUser = authInstance.currentUser.get();
            isGoogleDriveReady = true;
            
            // Update global state
            window.isGoogleDriveConnected = true;
            window.googleDriveUser = googleUser.getBasicProfile();
            
            updateGoogleDriveUI(true);
            startAutoSync();
            
            console.log('🔗 المستخدم مسجل دخول مسبقاً:', googleUser.getBasicProfile().getName());
            
            // Auto-download data for returning users
            setTimeout(async () => {
                console.log('📥 تحميل البيانات المحفوظة بعد تسجيل الدخول...');
                try {
                    await downloadProductsFromDrive();
                    await downloadCustomersFromDrive();
                    await downloadUsersFromDrive();
                    console.log('✅ تم تحميل البيانات المحفوظة');
                } catch (error) {
                    console.log('ℹ️ لا توجد بيانات محفوظة مسبقاً:', error.message);
                }
            }, 2000);
        } else {
            console.log('👤 المستخدم غير مسجل دخول');
            updateGoogleDriveUI(false);
        }
        
        isGoogleDriveReady = true;
        console.log('✅ تم تهيئة Google Drive بنجاح');
        return true;
        
    } catch (error) {
        console.error('❌ فشل في تهيئة Google Drive:', error);
        isGoogleDriveReady = false;
        updateGoogleDriveUI(false);
        
        // Show user-friendly error message
        if (typeof showToast === 'function') {
            showToast('فشل في تهيئة Google Drive. تحقق من الاتصال بالإنترنت.', 'error');
        }
        return false;
    }
}
```

### **4. تهيئة تلقائية متعددة المستويات** 🔄
```javascript
// Auto-initialize Google Drive when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 DOM جاهز - بدء تهيئة Google Drive...');
    
    setTimeout(() => {
        initializeGoogleDriveSync().then(success => {
            if (success) {
                console.log('🎉 تم تهيئة Google Drive تلقائياً');
            } else {
                console.log('⚠️ فشل في التهيئة التلقائية لـ Google Drive');
            }
        }).catch(error => {
            console.error('❌ خطأ في التهيئة التلقائية:', error);
        });
    }, 1000);
});

// Also try to initialize when window loads (fallback)
window.addEventListener('load', function() {
    if (!isGoogleDriveReady) {
        console.log('🔄 محاولة تهيئة Google Drive عند تحميل النافذة...');
        setTimeout(() => {
            initializeGoogleDriveSync();
        }, 2000);
    }
});

// Additional initialization in HTML
setTimeout(() => {
    if (typeof initializeGoogleDriveSync === 'function') {
        console.log('🔄 تهيئة Google Drive...');
        initializeGoogleDriveSync();
    } else {
        console.log('⚠️ وظيفة تهيئة Google Drive غير متاحة بعد');
        // Try again after scripts load
        setTimeout(() => {
            if (typeof initializeGoogleDriveSync === 'function') {
                console.log('🔄 تهيئة Google Drive (المحاولة الثانية)...');
                initializeGoogleDriveSync();
            }
        }, 2000);
    }
}, 1000);
```

### **5. زر اختبار الأزرار** 🧪
```html
<button type="button" class="btn btn-danger" onclick="testGoogleDriveButtons()" id="testGoogleDriveBtn">
    <i class="fas fa-bug"></i> اختبار الأزرار
</button>
```

```javascript
function testGoogleDriveButtons() {
    console.log('🧪 اختبار أزرار Google Drive...');
    
    // Check if Google API is loaded
    if (typeof gapi === 'undefined' || !window.gapi) {
        console.error('❌ Google API غير محمل');
        alert('❌ Google API غير محمل\n\nسيتم إعادة تحميل الصفحة لتحميل Google API...');
        setTimeout(() => {
            window.location.reload();
        }, 2000);
        return;
    }
    
    console.log('✅ Google API محمل');
    
    // Check if Google Drive functions exist
    const functions = [
        'connectToGoogleDrive',
        'uploadAllToGoogleDrive', 
        'downloadAllFromGoogleDrive',
        'checkGoogleDriveConnection',
        'initializeGoogleDriveSync'
    ];
    
    let missingFunctions = [];
    let availableFunctions = [];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            availableFunctions.push(funcName);
            console.log(`✅ ${funcName} متاحة`);
        } else {
            missingFunctions.push(funcName);
            console.error(`❌ ${funcName} غير متاحة`);
        }
    });
    
    // Check button elements
    const buttons = [
        { id: 'googleDriveConnectBtn', name: 'زر تسجيل الدخول' },
        { id: 'googleDriveUploadBtn', name: 'زر الرفع' },
        { id: 'googleDriveDownloadBtn', name: 'زر التحميل' },
        { id: 'googleDriveCheckBtn', name: 'زر فحص الاتصال' }
    ];
    
    let missingButtons = [];
    let availableButtons = [];
    
    buttons.forEach(btn => {
        const element = document.getElementById(btn.id);
        if (element) {
            availableButtons.push(btn.name);
            console.log(`✅ ${btn.name} موجود`);
            
            // Check if button is clickable
            const onclick = element.getAttribute('onclick');
            if (onclick) {
                console.log(`✅ ${btn.name} قابل للنقر: ${onclick}`);
            } else {
                console.warn(`⚠️ ${btn.name} لا يحتوي على onclick`);
            }
        } else {
            missingButtons.push(btn.name);
            console.error(`❌ ${btn.name} غير موجود`);
        }
    });
    
    // Show results
    let message = '🧪 نتائج اختبار أزرار Google Drive:\n\n';
    
    message += '✅ الوظائف المتاحة:\n';
    availableFunctions.forEach(func => {
        message += `  • ${func}\n`;
    });
    
    if (missingFunctions.length > 0) {
        message += '\n❌ الوظائف المفقودة:\n';
        missingFunctions.forEach(func => {
            message += `  • ${func}\n`;
        });
    }
    
    message += '\n✅ الأزرار المتاحة:\n';
    availableButtons.forEach(btn => {
        message += `  • ${btn}\n`;
    });
    
    if (missingButtons.length > 0) {
        message += '\n❌ الأزرار المفقودة:\n';
        missingButtons.forEach(btn => {
            message += `  • ${btn}\n`;
        });
    }
    
    // Check Google Drive initialization
    message += '\n🔍 حالة Google Drive:\n';
    message += `  • isGoogleDriveReady: ${window.isGoogleDriveReady || false}\n`;
    message += `  • isGoogleDriveConnected: ${window.isGoogleDriveConnected || false}\n`;
    message += `  • googleDriveUser: ${window.googleDriveUser ? 'متصل' : 'غير متصل'}\n`;
    
    alert(message);
    
    // Try to initialize if not ready
    if (!window.isGoogleDriveReady && typeof initializeGoogleDriveSync === 'function') {
        console.log('🔄 محاولة تهيئة Google Drive...');
        setTimeout(() => {
            initializeGoogleDriveSync();
        }, 1000);
    }
}
```

## 🔧 **خطوات الإصلاح للمستخدم:**

### **الحل السريع:**
```
1. افتح التطبيق
2. اذهب إلى الإعدادات → نظام التخزين السحابي
3. اضغط "اختبار الأزرار" 🧪
4. إذا ظهرت رسالة خطأ، اضغط "موافق" لإعادة التحميل
5. بعد إعادة التحميل، جرب الأزرار مرة أخرى ✅
```

### **الحل المتقدم:**
```
1. اضغط F12 لفتح Developer Tools
2. في Console اكتب: initializeGoogleDriveSync()
3. انتظر رسالة "✅ تم تهيئة Google Drive بنجاح"
4. جرب أزرار Google Drive الآن ✅
```

### **إذا لم تعمل الأزرار:**
```
1. تأكد من الاتصال بالإنترنت 🌐
2. امسح cache المتصفح (Ctrl+Shift+Delete)
3. أعد تحميل الصفحة بقوة (Ctrl+F5)
4. انتظر 5 ثوان ثم جرب الأزرار
5. إذا لم تعمل، اضغط "اختبار الأزرار" للتشخيص
```

## 📊 **رسائل التشخيص:**

### **عند نجاح التهيئة:**
```
🚀 تهيئة نظام Google Drive...
📥 تحميل Google API...
✅ تم تحميل Google API
✅ تم تحميل مكونات Google API
✅ تم تهيئة Google Client
👤 المستخدم غير مسجل دخول
✅ تم تهيئة Google Drive بنجاح
🎉 تم تهيئة Google Drive تلقائياً
```

### **عند فشل التهيئة:**
```
❌ فشل في تحميل Google API: [error details]
❌ فشل في تهيئة Google Drive: [error details]
⚠️ فشل في التهيئة التلقائية لـ Google Drive
```

### **عند اختبار الأزرار:**
```
🧪 اختبار أزرار Google Drive...
✅ Google API محمل
✅ connectToGoogleDrive متاحة
✅ uploadAllToGoogleDrive متاحة
✅ downloadAllFromGoogleDrive متاحة
✅ checkGoogleDriveConnection متاحة
✅ initializeGoogleDriveSync متاحة
✅ زر تسجيل الدخول موجود
✅ زر الرفع موجود
✅ زر التحميل موجود
✅ زر فحص الاتصال موجود
```

## 🎯 **النتيجة المتوقعة:**

### **بعد تطبيق الإصلاح:**
- ✅ **تسجيل دخول Google** يعمل بشكل طبيعي
- ✅ **رفع جميع البيانات** يعمل بعد تسجيل الدخول
- ✅ **تحميل جميع البيانات** يعمل بعد تسجيل الدخول
- ✅ **فحص الاتصال** يعمل ويظهر حالة الاتصال
- ✅ **مزامنة المستخدمين** تعمل لحل مشكلة تسجيل الدخول من أجهزة متعددة

### **تسلسل العمل الطبيعي:**
```
1. 🔵 اضغط "تسجيل دخول Google"
2. 🌐 يفتح نافذة تسجيل الدخول
3. ✅ سجل دخول بحساب Google
4. 🟢 تصبح الأزرار نشطة
5. 📤 اضغط "رفع جميع البيانات" لحفظ البيانات
6. 📥 اضغط "تحميل جميع البيانات" في الأجهزة الأخرى
7. 🔄 تعمل المزامنة التلقائية كل 30 ثانية
```

**🌟 الآن أزرار Google Drive تعمل بشكل كامل ويمكن مزامنة البيانات بين الأجهزة!**
