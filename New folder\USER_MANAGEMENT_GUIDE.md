# 👥 دليل إدارة المستخدمين والصلاحيات - النسور الماسية

## 🎯 نظرة عامة

تم إضافة نظام شامل لإدارة المستخدمين مع تحديد الصلاحيات لكل مستخدم، مما يتيح:

- ✅ **إضافة مستخدمين جدد** مع أدوار مختلفة
- ✅ **تحديد صلاحيات مخصصة** لكل مستخدم
- ✅ **إخفاء/إظهار الأقسام** حسب الصلاحيات
- ✅ **أمان متقدم** مع مستويات وصول مختلفة

## 🔐 أنواع المستخدمين والأدوار

### 1. **المدير (Admin)**
- 🔑 **صلاحيات كاملة** على جميع الأقسام
- 👥 **إدارة المستخدمين** (إضافة، تعديل، حذف)
- ⚙️ **إعدادات النظام** الكاملة
- 🛠️ **أدوات المطورين** والنظام

### 2. **مدير القسم (Manager)**
- 📊 **عرض لوحة التحكم**
- 📦 **إدارة المنتجات** (عرض، إضافة، تعديل)
- 👤 **إدارة العملاء** (عرض، إضافة، تعديل)
- ⚙️ **عرض الإعدادات** (بدون تعديل)

### 3. **الموظف (Employee)**
- 📊 **عرض لوحة التحكم**
- 📦 **إضافة منتجات** وعرضها
- 👤 **إضافة عملاء** وعرضهم
- ❌ **لا يمكن التعديل أو الحذف**

### 4. **المشاهد (Viewer)**
- 📊 **عرض لوحة التحكم** فقط
- 👁️ **عرض المنتجات** (بدون إضافة أو تعديل)
- 👁️ **عرض العملاء** (بدون إضافة أو تعديل)
- ❌ **لا يمكن الوصول للإعدادات**

## 🚀 كيفية إضافة مستخدم جديد

### الخطوة 1: الوصول لإدارة المستخدمين
```
1. سجل دخول كمدير
2. اذهب إلى الإعدادات
3. ابحث عن قسم "إدارة المستخدمين والصلاحيات"
4. اضغط "إضافة مستخدم جديد"
```

### الخطوة 2: ملء بيانات المستخدم
```
📝 الاسم الكامل: اسم المستخدم
📧 البريد الإلكتروني: <EMAIL>
🔑 كلمة المرور: كلمة مرور قوية
👤 الدور: اختر من القائمة
```

### الخطوة 3: تحديد الصلاحيات
```
🔄 الطريقة الأولى: استخدام الأدوار المحددة مسبقاً
✅ الطريقة الثانية: تخصيص الصلاحيات يدوياً
```

### الخطوة 4: حفظ المستخدم
```
✅ اضغط "إضافة المستخدم"
📧 سيتم إنشاء المستخدم فوراً
🔐 يمكن للمستخدم تسجيل الدخول الآن
```

## 🔧 تخصيص الصلاحيات

### الصلاحيات المتاحة:

#### **لوحة التحكم:**
- ✅ **عرض لوحة التحكم** - الوصول للصفحة الرئيسية

#### **المنتجات:**
- ✅ **عرض المنتجات** - رؤية قائمة المنتجات
- ✅ **إضافة منتجات** - إضافة منتجات جديدة
- ✅ **تعديل منتجات** - تعديل المنتجات الموجودة
- ✅ **حذف منتجات** - حذف المنتجات

#### **العملاء:**
- ✅ **عرض العملاء** - رؤية قائمة العملاء
- ✅ **إضافة عملاء** - إضافة عملاء جدد
- ✅ **تعديل عملاء** - تعديل بيانات العملاء
- ✅ **حذف عملاء** - حذف العملاء

#### **الإعدادات:**
- ✅ **عرض الإعدادات** - الوصول لصفحة الإعدادات
- ✅ **تعديل الإعدادات** - تغيير إعدادات النظام
- ✅ **إدارة المستخدمين** - إضافة وتعديل المستخدمين

## 🎛️ كيف تعمل الصلاحيات

### **إخفاء الأقسام:**
```
❌ إذا لم يكن للمستخدم صلاحية "عرض المنتجات"
   → لن يظهر قسم المنتجات في القائمة
   → لن يظهر في لوحة التحكم
```

### **إخفاء الأزرار:**
```
❌ إذا لم يكن للمستخدم صلاحية "إضافة منتجات"
   → لن يظهر زر "إضافة منتج جديد"
   → لن يظهر في أي مكان في التطبيق
```

### **منع الوصول:**
```
🚫 إذا حاول المستخدم الوصول لقسم بدون صلاحية
   → سيظهر رسالة خطأ
   → سيتم منعه من الوصول
```

## 👥 إدارة المستخدمين الموجودين

### **عرض تفاصيل المستخدم:**
```
👁️ اضغط زر "عرض" بجانب اسم المستخدم
📋 ستظهر جميع التفاصيل والصلاحيات
```

### **تعديل المستخدم:**
```
✏️ اضغط زر "تعديل" بجانب اسم المستخدم
🔄 يمكن تغيير الاسم، الإيميل، الدور، الصلاحيات
💾 احفظ التغييرات
```

### **حذف المستخدم:**
```
🗑️ اضغط زر "حذف" بجانب اسم المستخدم
⚠️ تأكيد الحذف (لا يمكن التراجع)
✅ سيتم حذف المستخدم نهائياً
```

## 🔒 الأمان والحماية

### **حماية المدير الرئيسي:**
- ❌ **لا يمكن حذف** آخر مدير في النظام
- 🔐 **المدير الافتراضي** محمي من الحذف
- 🛡️ **صلاحيات المدير** لا يمكن إزالتها

### **تشفير كلمات المرور:**
- 🔐 **كلمات المرور محفوظة** بشكل آمن
- 🔄 **يمكن تغيير كلمة المرور** في أي وقت
- 🚫 **لا يمكن عرض كلمات المرور** المحفوظة

### **جلسات المستخدمين:**
- ⏰ **انتهاء الجلسة** بعد فترة محددة
- 🔄 **تسجيل آخر دخول** لكل مستخدم
- 📊 **تتبع النشاط** والاستخدام

## 📱 أمثلة عملية

### **مثال 1: موظف استقبال**
```
👤 الدور: موظف (Employee)
✅ الصلاحيات:
   - عرض لوحة التحكم
   - عرض المنتجات
   - إضافة عملاء جدد
   - عرض العملاء
❌ لا يمكنه:
   - إضافة أو تعديل منتجات
   - حذف أي بيانات
   - الوصول للإعدادات
```

### **مثال 2: مدير المبيعات**
```
👤 الدور: مدير قسم (Manager)
✅ الصلاحيات:
   - جميع صلاحيات الموظف
   - إضافة وتعديل المنتجات
   - تعديل بيانات العملاء
   - عرض الإعدادات
❌ لا يمكنه:
   - حذف المنتجات أو العملاء
   - إدارة المستخدمين
   - تعديل إعدادات النظام
```

### **مثال 3: مراجع خارجي**
```
👤 الدور: مشاهد (Viewer)
✅ الصلاحيات:
   - عرض لوحة التحكم فقط
   - عرض المنتجات (بدون تعديل)
   - عرض العملاء (بدون تعديل)
❌ لا يمكنه:
   - إضافة أو تعديل أي بيانات
   - الوصول للإعدادات
   - أي عمليات تغيير
```

## 🎊 النتيجة النهائية

بعد إضافة نظام إدارة المستخدمين:

- ✅ **أمان متقدم** مع مستويات وصول مختلفة
- ✅ **مرونة كاملة** في تحديد الصلاحيات
- ✅ **واجهة تتكيف** مع صلاحيات كل مستخدم
- ✅ **حماية البيانات** من الوصول غير المصرح
- ✅ **إدارة فريق العمل** بكفاءة عالية

**🌟 الآن يمكن لكل مستخدم رؤية الأقسام المسموح له بها فقط!**
