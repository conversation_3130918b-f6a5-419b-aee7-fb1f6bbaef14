// API Integration System for Diamond Eagles Inventory
// نظام تكامل APIs لتطبيق النسور الماسية

console.log('🌐 تحميل نظام تكامل APIs...');

// API Configuration
const API_CONFIG = {
    // Google Drive API (existing)
    googleDrive: {
        enabled: true,
        clientId: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
        apiKey: 'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8',
        scope: 'https://www.googleapis.com/auth/drive.file'
    },
    
    // REST API for external integrations
    restApi: {
        enabled: false,
        baseUrl: 'https://your-api-server.com/api',
        apiKey: '',
        endpoints: {
            products: '/products',
            customers: '/customers',
            users: '/users',
            sync: '/sync'
        }
    },
    
    // Webhook notifications
    webhooks: {
        enabled: false,
        urls: {
            productUpdate: '',
            customerUpdate: '',
            lowStock: ''
        }
    },
    
    // Email API (for notifications)
    email: {
        enabled: false,
        service: 'emailjs', // or 'smtp'
        serviceId: '',
        templateId: '',
        publicKey: ''
    }
};

// API Status tracking
let apiStatus = {
    googleDrive: false,
    restApi: false,
    webhooks: false,
    email: false
};

/**
 * Initialize all APIs
 */
async function initializeAPIs() {
    console.log('🚀 تهيئة جميع APIs...');
    
    try {
        // Initialize Google Drive API (already exists)
        if (API_CONFIG.googleDrive.enabled) {
            await initializeGoogleDriveAPI();
        }
        
        // Initialize REST API
        if (API_CONFIG.restApi.enabled) {
            await initializeRestAPI();
        }
        
        // Initialize Email API
        if (API_CONFIG.email.enabled) {
            await initializeEmailAPI();
        }
        
        // Initialize Webhooks
        if (API_CONFIG.webhooks.enabled) {
            initializeWebhooks();
        }
        
        console.log('✅ تم تهيئة جميع APIs بنجاح');
        updateAPIStatus();
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة APIs:', error);
    }
}

/**
 * Initialize Google Drive API (enhanced)
 */
async function initializeGoogleDriveAPI() {
    try {
        console.log('🔄 تهيئة Google Drive API...');

        // Check if Google Drive functions are available
        if (typeof initializeGoogleDriveSync === 'function') {
            await initializeGoogleDriveSync();
            apiStatus.googleDrive = true;
            console.log('✅ Google Drive API جاهز');
        } else if (typeof initializeGoogleDrive === 'function') {
            await initializeGoogleDrive();
            apiStatus.googleDrive = true;
            console.log('✅ Google Drive API جاهز (الطريقة البديلة)');
        } else {
            // Check if already connected
            if (window.isGoogleDriveConnected) {
                apiStatus.googleDrive = true;
                console.log('✅ Google Drive متصل مسبقاً');
            } else {
                console.log('⚠️ Google Drive غير متصل');
                apiStatus.googleDrive = false;
            }
        }
    } catch (error) {
        console.error('❌ خطأ في Google Drive API:', error);
        apiStatus.googleDrive = false;
    }
}

/**
 * Initialize REST API
 */
async function initializeRestAPI() {
    if (!API_CONFIG.restApi.baseUrl || !API_CONFIG.restApi.apiKey) {
        console.log('⚠️ REST API غير مكون');
        return;
    }
    
    try {
        // Test API connection
        const response = await fetch(`${API_CONFIG.restApi.baseUrl}/health`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${API_CONFIG.restApi.apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            apiStatus.restApi = true;
            console.log('✅ REST API متصل');
        } else {
            throw new Error('فشل في الاتصال بـ REST API');
        }
    } catch (error) {
        console.error('❌ خطأ في REST API:', error);
        apiStatus.restApi = false;
    }
}

/**
 * Initialize Email API
 */
async function initializeEmailAPI() {
    if (API_CONFIG.email.service === 'emailjs') {
        try {
            // Load EmailJS if not loaded
            if (typeof emailjs === 'undefined') {
                await loadEmailJS();
            }
            
            emailjs.init(API_CONFIG.email.publicKey);
            apiStatus.email = true;
            console.log('✅ Email API جاهز');
        } catch (error) {
            console.error('❌ خطأ في Email API:', error);
            apiStatus.email = false;
        }
    }
}

/**
 * Initialize Webhooks
 */
function initializeWebhooks() {
    // Webhooks are passive, just mark as ready if URLs are configured
    const hasWebhooks = Object.values(API_CONFIG.webhooks.urls).some(url => url);
    apiStatus.webhooks = hasWebhooks;
    
    if (hasWebhooks) {
        console.log('✅ Webhooks مكونة');
    }
}

/**
 * Sync data to external REST API
 */
async function syncToRestAPI(dataType, data) {
    if (!apiStatus.restApi) {
        console.log('⚠️ REST API غير متاح');
        return false;
    }
    
    try {
        const endpoint = API_CONFIG.restApi.endpoints[dataType];
        if (!endpoint) {
            throw new Error(`نقطة نهاية غير معروفة: ${dataType}`);
        }
        
        const response = await fetch(`${API_CONFIG.restApi.baseUrl}${endpoint}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_CONFIG.restApi.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            console.log(`✅ تم مزامنة ${dataType} مع REST API`);
            return true;
        } else {
            throw new Error(`فشل في مزامنة ${dataType}`);
        }
    } catch (error) {
        console.error(`❌ خطأ في مزامنة ${dataType}:`, error);
        return false;
    }
}

/**
 * Send webhook notification
 */
async function sendWebhook(type, data) {
    const webhookUrl = API_CONFIG.webhooks.urls[type];
    if (!webhookUrl || !apiStatus.webhooks) {
        return false;
    }
    
    try {
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: type,
                timestamp: new Date().toISOString(),
                data: data
            })
        });
        
        if (response.ok) {
            console.log(`✅ تم إرسال webhook: ${type}`);
            return true;
        }
    } catch (error) {
        console.error(`❌ خطأ في webhook ${type}:`, error);
    }
    
    return false;
}

/**
 * Send email notification
 */
async function sendEmailNotification(templateParams) {
    if (!apiStatus.email) {
        console.log('⚠️ Email API غير متاح');
        return false;
    }
    
    try {
        const response = await emailjs.send(
            API_CONFIG.email.serviceId,
            API_CONFIG.email.templateId,
            templateParams
        );
        
        console.log('✅ تم إرسال البريد الإلكتروني');
        return true;
    } catch (error) {
        console.error('❌ خطأ في إرسال البريد:', error);
        return false;
    }
}

/**
 * Enhanced data sync with multiple APIs
 */
async function syncDataToAllAPIs(dataType, data) {
    const results = {
        googleDrive: false,
        restApi: false,
        webhook: false
    };
    
    // Sync to Google Drive (existing functionality)
    if (apiStatus.googleDrive) {
        try {
            if (dataType === 'products' && typeof uploadProductsToDrive === 'function') {
                results.googleDrive = await uploadProductsToDrive();
            } else if (dataType === 'customers' && typeof uploadCustomersToDrive === 'function') {
                results.googleDrive = await uploadCustomersToDrive();
            }
        } catch (error) {
            console.error('❌ خطأ في Google Drive sync:', error);
        }
    }
    
    // Sync to REST API
    if (apiStatus.restApi) {
        results.restApi = await syncToRestAPI(dataType, data);
    }
    
    // Send webhook notification
    if (apiStatus.webhooks) {
        results.webhook = await sendWebhook(`${dataType}Update`, data);
    }
    
    return results;
}

/**
 * Load EmailJS library
 */
function loadEmailJS() {
    return new Promise((resolve, reject) => {
        if (document.querySelector('script[src*="emailjs"]')) {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * Check Google Drive connection status
 */
function checkGoogleDriveStatus() {
    try {
        // Check multiple indicators
        const indicators = [
            window.isGoogleDriveConnected,
            window.isGoogleDriveReady,
            typeof gapi !== 'undefined' && gapi.auth2 && gapi.auth2.getAuthInstance && gapi.auth2.getAuthInstance() && gapi.auth2.getAuthInstance().isSignedIn.get()
        ];

        const isConnected = indicators.some(indicator => indicator === true);
        apiStatus.googleDrive = isConnected;

        console.log('🔍 فحص حالة Google Drive:', {
            isGoogleDriveConnected: window.isGoogleDriveConnected,
            isGoogleDriveReady: window.isGoogleDriveReady,
            gapiAvailable: typeof gapi !== 'undefined',
            authInstance: typeof gapi !== 'undefined' && gapi.auth2 && gapi.auth2.getAuthInstance(),
            finalStatus: isConnected
        });

        return isConnected;
    } catch (error) {
        console.error('❌ خطأ في فحص Google Drive:', error);
        apiStatus.googleDrive = false;
        return false;
    }
}

/**
 * Update API status in UI
 */
function updateAPIStatus() {
    // Check Google Drive status specifically
    checkGoogleDriveStatus();

    // Update status indicators if they exist
    Object.keys(apiStatus).forEach(api => {
        const statusElement = document.getElementById(`${api}Status`);
        if (statusElement) {
            statusElement.className = apiStatus[api] ? 'api-status connected' : 'api-status disconnected';
            statusElement.textContent = apiStatus[api] ? 'متصل' : 'غير متصل';
        }
    });

    // Show overall API status
    const connectedAPIs = Object.values(apiStatus).filter(status => status).length;
    const totalAPIs = Object.keys(apiStatus).length;

    console.log(`📊 حالة APIs: ${connectedAPIs}/${totalAPIs} متصل`);
}

/**
 * Get API configuration for external use
 */
function getAPIConfig() {
    return {
        config: API_CONFIG,
        status: apiStatus
    };
}

/**
 * Update API configuration
 */
function updateAPIConfig(newConfig) {
    Object.assign(API_CONFIG, newConfig);
    console.log('⚙️ تم تحديث إعدادات API');
}

// Export functions to global scope
window.initializeAPIs = initializeAPIs;
window.syncDataToAllAPIs = syncDataToAllAPIs;
window.sendWebhook = sendWebhook;
window.sendEmailNotification = sendEmailNotification;
window.getAPIConfig = getAPIConfig;
window.updateAPIConfig = updateAPIConfig;
window.apiStatus = apiStatus;

// Auto-initialize on load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeAPIs();
    }, 2000);
});

console.log('✅ تم تحميل نظام تكامل APIs');
