<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مزامنة البيانات - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .diagnosis-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        .section h3 {
            margin-top: 0;
            color: #17a2b8;
        }
        button {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
            font-weight: 500;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin: 15px 0;
            font-size: 12px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .status-card.connected {
            border-color: #28a745;
            background: #d4edda;
        }
        .status-card.disconnected {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .data-count {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .data-count.good { color: #28a745; }
        .data-count.warning { color: #ffc107; }
        .data-count.danger { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="diagnosis-icon">
                🔍
            </div>
            <h1>🔍 تشخيص مزامنة البيانات</h1>
            <p><strong>فحص شامل لمشاكل مشاركة البيانات بين المستخدمين</strong></p>
        </div>

        <div class="section">
            <h3>📊 حالة البيانات الحالية</h3>
            <div class="status-grid" id="dataStatus">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button onclick="checkDataStatus()">🔍 فحص البيانات</button>
        </div>

        <div class="section">
            <h3>🌐 حالة Google Drive</h3>
            <div id="googleDriveStatus"></div>
            <button onclick="checkGoogleDriveStatus()">🔍 فحص Google Drive</button>
            <button onclick="testGoogleDriveConnection()" class="btn-warning">🧪 اختبار الاتصال</button>
        </div>

        <div class="section">
            <h3>👤 معلومات المستخدم الحالي</h3>
            <div id="userInfo"></div>
            <button onclick="checkUserInfo()">🔍 فحص المستخدم</button>
        </div>

        <div class="section">
            <h3>🔄 عمليات المزامنة</h3>
            <button onclick="uploadAllData()" class="btn-success">📤 رفع جميع البيانات</button>
            <button onclick="downloadAllData()" class="btn-success">📥 تحميل جميع البيانات</button>
            <button onclick="forceSync()" class="btn-warning">⚡ مزامنة إجبارية</button>
            <button onclick="clearLocalData()" class="btn-danger">🗑️ مسح البيانات المحلية</button>
        </div>

        <div class="section">
            <h3>🛠️ أدوات الإصلاح</h3>
            <button onclick="fixDataSharing()" class="btn-warning">🔧 إصلاح مشاركة البيانات</button>
            <button onclick="resetAndSync()" class="btn-danger">🔄 إعادة تعيين ومزامنة</button>
            <button onclick="createSharedAccount()" class="btn-success">👥 إنشاء حساب مشترك</button>
        </div>

        <div class="section">
            <h3>📝 سجل العمليات</h3>
            <div id="output"></div>
            <button onclick="clearOutput()">🗑️ مسح السجل</button>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="goToMainApp()" style="background: #6c757d;">
                🏠 العودة للتطبيق الرئيسي
            </button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            log('تم مسح السجل');
        }

        function checkDataStatus() {
            log('🔍 فحص حالة البيانات المحلية...');
            
            const dataStatus = document.getElementById('dataStatus');
            dataStatus.innerHTML = '';

            // Check products
            const products = JSON.parse(localStorage.getItem('products') || '[]');
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');

            // Create status cards
            const statusCards = [
                {
                    title: 'المنتجات',
                    count: products.length,
                    icon: '📦',
                    data: 'products'
                },
                {
                    title: 'العملاء',
                    count: customers.length,
                    icon: '👥',
                    data: 'customers'
                },
                {
                    title: 'المستخدمين',
                    count: users.length,
                    icon: '👤',
                    data: 'users'
                }
            ];

            statusCards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = 'status-card';
                
                let countClass = 'danger';
                if (card.count > 0) countClass = 'good';
                else if (card.count === 0) countClass = 'warning';

                cardElement.innerHTML = `
                    <div>${card.icon}</div>
                    <h4>${card.title}</h4>
                    <div class="data-count ${countClass}">${card.count}</div>
                `;
                
                dataStatus.appendChild(cardElement);
            });

            log(`📊 البيانات المحلية: ${products.length} منتج، ${customers.length} عميل، ${users.length} مستخدم`);
        }

        function checkGoogleDriveStatus() {
            log('🔍 فحص حالة Google Drive...');
            
            const statusDiv = document.getElementById('googleDriveStatus');
            let statusHTML = '';

            // Check connection status
            const isConnected = window.isGoogleDriveConnected || false;
            const isReady = window.isGoogleDriveReady || false;
            const user = window.googleDriveUser || null;

            statusHTML += `<div class="status-card ${isConnected ? 'connected' : 'disconnected'}">`;
            statusHTML += `<h4>🔗 حالة الاتصال</h4>`;
            statusHTML += `<p>${isConnected ? '✅ متصل' : '❌ غير متصل'}</p>`;
            statusHTML += `</div>`;

            statusHTML += `<div class="status-card ${isReady ? 'connected' : 'disconnected'}">`;
            statusHTML += `<h4>⚙️ حالة التهيئة</h4>`;
            statusHTML += `<p>${isReady ? '✅ جاهز' : '❌ غير جاهز'}</p>`;
            statusHTML += `</div>`;

            statusHTML += `<div class="status-card ${user ? 'connected' : 'disconnected'}">`;
            statusHTML += `<h4>👤 المستخدم</h4>`;
            statusHTML += `<p>${user ? `✅ ${user.name || user.email || 'مسجل دخول'}` : '❌ غير مسجل'}</p>`;
            statusHTML += `</div>`;

            statusDiv.innerHTML = statusHTML;

            log(`🌐 Google Drive: اتصال=${isConnected}, جاهز=${isReady}, مستخدم=${user ? 'موجود' : 'غير موجود'}`);
        }

        function checkUserInfo() {
            log('🔍 فحص معلومات المستخدم الحالي...');
            
            const userInfoDiv = document.getElementById('userInfo');
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const loginTime = localStorage.getItem('loginTime');

            let userHTML = '<div class="status-grid">';
            
            userHTML += `<div class="status-card ${isLoggedIn ? 'connected' : 'disconnected'}">`;
            userHTML += `<h4>🔐 حالة تسجيل الدخول</h4>`;
            userHTML += `<p>${isLoggedIn ? '✅ مسجل دخول' : '❌ غير مسجل'}</p>`;
            userHTML += `</div>`;

            userHTML += `<div class="status-card ${currentUser ? 'connected' : 'disconnected'}">`;
            userHTML += `<h4>👤 المستخدم الحالي</h4>`;
            userHTML += `<p>${currentUser ? currentUser.name || currentUser.email : 'غير محدد'}</p>`;
            userHTML += `</div>`;

            userHTML += `<div class="status-card">`;
            userHTML += `<h4>⏰ وقت تسجيل الدخول</h4>`;
            userHTML += `<p>${loginTime ? new Date(loginTime).toLocaleString('ar') : 'غير محدد'}</p>`;
            userHTML += `</div>`;

            userHTML += '</div>';
            userInfoDiv.innerHTML = userHTML;

            log(`👤 المستخدم: ${currentUser ? currentUser.name || currentUser.email : 'غير محدد'}`);
        }

        function uploadAllData() {
            log('📤 بدء رفع جميع البيانات...');
            
            if (typeof uploadAllToGoogleDrive === 'function') {
                uploadAllToGoogleDrive().then(() => {
                    log('✅ تم رفع جميع البيانات بنجاح', 'success');
                }).catch(error => {
                    log(`❌ فشل في رفع البيانات: ${error.message}`, 'error');
                });
            } else {
                log('❌ وظيفة الرفع غير متاحة', 'error');
            }
        }

        function downloadAllData() {
            log('📥 بدء تحميل جميع البيانات...');
            
            if (typeof downloadAllFromGoogleDrive === 'function') {
                downloadAllFromGoogleDrive().then(() => {
                    log('✅ تم تحميل جميع البيانات بنجاح', 'success');
                    checkDataStatus(); // Refresh data status
                }).catch(error => {
                    log(`❌ فشل في تحميل البيانات: ${error.message}`, 'error');
                });
            } else {
                log('❌ وظيفة التحميل غير متاحة', 'error');
            }
        }

        function forceSync() {
            log('⚡ بدء المزامنة الإجبارية...');
            
            // First upload, then download
            uploadAllData();
            setTimeout(() => {
                downloadAllData();
            }, 3000);
        }

        function clearLocalData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحلية؟\nسيتم الاحتفاظ بالبيانات في Google Drive فقط.')) {
                localStorage.removeItem('products');
                localStorage.removeItem('customers');
                localStorage.removeItem('systemUsers');
                
                log('🗑️ تم مسح البيانات المحلية', 'warning');
                checkDataStatus();
            }
        }

        function fixDataSharing() {
            log('🔧 بدء إصلاح مشاركة البيانات...');
            
            // Step 1: Check Google Drive connection
            if (!window.isGoogleDriveConnected) {
                log('⚠️ Google Drive غير متصل - محاولة الاتصال...', 'warning');
                if (typeof connectToGoogleDrive === 'function') {
                    connectToGoogleDrive();
                }
                return;
            }
            
            // Step 2: Upload current data
            log('📤 رفع البيانات الحالية...');
            uploadAllData();
            
            // Step 3: Enable auto-sync
            log('🔄 تفعيل المزامنة التلقائية...');
            if (typeof startAutoSync === 'function') {
                startAutoSync();
            }
            
            log('✅ تم إصلاح مشاركة البيانات', 'success');
        }

        function resetAndSync() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات والمزامنة من Google Drive؟')) {
                log('🔄 بدء إعادة التعيين والمزامنة...', 'warning');
                
                // Clear local data
                clearLocalData();
                
                // Download from Google Drive
                setTimeout(() => {
                    downloadAllData();
                }, 2000);
            }
        }

        function createSharedAccount() {
            log('👥 إرشادات إنشاء حساب مشترك...');
            
            const instructions = `
📋 خطوات إنشاء حساب مشترك:

1. استخدم نفس حساب Google لجميع المستخدمين
2. الحساب المقترح: <EMAIL>
3. جميع المستخدمين يسجلون دخول بنفس الحساب
4. البيانات ستكون مشتركة تلقائياً

⚠️ تأكد من:
- تسجيل دخول جميع المستخدمين بنفس الحساب
- تفعيل المزامنة التلقائية
- رفع البيانات من الجهاز الرئيسي أولاً
            `;
            
            log(instructions);
            alert(instructions);
        }

        function testGoogleDriveConnection() {
            log('🧪 اختبار اتصال Google Drive...');
            
            if (typeof testGoogleDriveConnection === 'function') {
                testGoogleDriveConnection();
            } else {
                log('❌ وظيفة اختبار الاتصال غير متاحة', 'error');
            }
        }

        function goToMainApp() {
            window.location.href = 'index.html';
        }

        // Auto-run initial checks
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 تم تحميل أداة تشخيص مزامنة البيانات');
                checkDataStatus();
                checkGoogleDriveStatus();
                checkUserInfo();
            }, 1000);
        });
    </script>
</body>
</html>
