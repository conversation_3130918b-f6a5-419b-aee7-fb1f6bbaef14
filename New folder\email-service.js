// Email Service for Password Recovery
// شركة النسور الماسية للتجارة - خدمة البريد الإلكتروني

class EmailService {
    constructor() {
        this.isInitialized = false;
        this.serviceConfig = {
            // EmailJS Configuration
            emailjs: {
                publicKey: 'YOUR_EMAILJS_PUBLIC_KEY',
                serviceId: 'YOUR_EMAILJS_SERVICE_ID',
                templateId: 'YOUR_EMAILJS_TEMPLATE_ID'
            },
            
            // Backup SMTP Configuration (if needed)
            smtp: {
                host: 'smtp.gmail.com',
                port: 587,
                secure: false
            }
        };
        
        this.emailTemplates = {
            passwordRecovery: {
                subject: 'استرداد كلمة المرور - شركة النسور الماسية للتجارة',
                template: this.getPasswordRecoveryTemplate()
            },
            welcomeEmail: {
                subject: 'مرحباً بك في نظام النسور الماسية للتجارة',
                template: this.getWelcomeTemplate()
            }
        };
    }

    // Initialize EmailJS
    async initialize() {
        try {
            if (typeof emailjs === 'undefined') {
                console.log('📧 تحميل EmailJS...');
                await this.loadEmailJS();
            }
            
            // Initialize with public key
            emailjs.init(this.serviceConfig.emailjs.publicKey);
            this.isInitialized = true;
            console.log('✅ تم تهيئة خدمة البريد الإلكتروني');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة خدمة البريد:', error);
            return false;
        }
    }

    // Load EmailJS library dynamically
    loadEmailJS() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Send password recovery email
    async sendPasswordRecovery(userEmail, userData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            const templateParams = {
                to_email: userEmail,
                to_name: userData.name,
                user_name: userData.name,
                user_email: userData.email,
                user_password: userData.password,
                company_name: 'شركة النسور الماسية للتجارة',
                recovery_date: new Date().toLocaleDateString('ar-SA'),
                recovery_time: new Date().toLocaleTimeString('ar-SA'),
                app_url: window.location.origin,
                support_email: '<EMAIL>',
                support_phone: '01022225982'
            };

            const result = await emailjs.send(
                this.serviceConfig.emailjs.serviceId,
                this.serviceConfig.emailjs.templateId,
                templateParams
            );

            console.log('✅ تم إرسال البريد بنجاح:', result);
            return {
                success: true,
                message: 'تم إرسال كلمة المرور بنجاح',
                result: result
            };

        } catch (error) {
            console.error('❌ خطأ في إرسال البريد:', error);
            return {
                success: false,
                message: 'فشل في إرسال البريد الإلكتروني',
                error: error
            };
        }
    }

    // Send welcome email for new users
    async sendWelcomeEmail(userEmail, userData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            const templateParams = {
                to_email: userEmail,
                to_name: userData.name,
                user_name: userData.name,
                user_email: userData.email,
                company_name: 'شركة النسور الماسية للتجارة',
                registration_date: new Date().toLocaleDateString('ar-SA'),
                app_url: window.location.origin,
                support_email: '<EMAIL>'
            };

            const result = await emailjs.send(
                this.serviceConfig.emailjs.serviceId,
                'welcome_template', // Different template for welcome
                templateParams
            );

            return {
                success: true,
                message: 'تم إرسال بريد الترحيب بنجاح',
                result: result
            };

        } catch (error) {
            console.error('❌ خطأ في إرسال بريد الترحيب:', error);
            return {
                success: false,
                message: 'فشل في إرسال بريد الترحيب',
                error: error
            };
        }
    }

    // Get password recovery email template
    getPasswordRecoveryTemplate() {
        return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f8f9fa; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">🔑 استرداد كلمة المرور</h1>
                <p style="margin: 10px 0 0 0;">شركة النسور الماسية للتجارة</p>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <h2 style="color: #333; margin-top: 0;">مرحباً {{user_name}}</h2>
                
                <p style="color: #666; line-height: 1.6;">
                    تم طلب استرداد كلمة المرور لحسابك في نظام إدارة المخزون.
                </p>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <h3 style="color: #667eea; margin-top: 0;">بيانات تسجيل الدخول:</h3>
                    <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{user_email}}</p>
                    <p style="margin: 5px 0;"><strong>كلمة المرور:</strong> <span style="background: #e9ecef; padding: 5px 10px; border-radius: 4px; font-family: monospace;">{{user_password}}</span></p>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{app_url}}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                        🚀 تسجيل الدخول الآن
                    </a>
                </div>
                
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="color: #856404; margin-top: 0;">⚠️ تعليمات الأمان:</h4>
                    <ul style="color: #856404; margin: 0; padding-right: 20px;">
                        <li>لا تشارك كلمة المرور مع أي شخص</li>
                        <li>قم بتغيير كلمة المرور بعد تسجيل الدخول</li>
                        <li>إذا لم تطلب هذا البريد، تجاهله</li>
                    </ul>
                </div>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p><strong>شركة النسور الماسية للتجارة</strong></p>
                    <p>نظام إدارة مخزون بطاريات الدواجن</p>
                    <p>📧 {{support_email}} | 📞 {{support_phone}}</p>
                    <p style="font-size: 12px; color: #999;">تم الإرسال في {{recovery_date}} الساعة {{recovery_time}}</p>
                </div>
            </div>
        </div>
        `;
    }

    // Get welcome email template
    getWelcomeTemplate() {
        return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f8f9fa; padding: 20px;">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">🎉 مرحباً بك</h1>
                <p style="margin: 10px 0 0 0;">شركة النسور الماسية للتجارة</p>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <h2 style="color: #333; margin-top: 0;">أهلاً وسهلاً {{user_name}}</h2>
                
                <p style="color: #666; line-height: 1.6;">
                    نرحب بك في نظام إدارة مخزون بطاريات الدواجن الخاص بشركة النسور الماسية للتجارة.
                </p>
                
                <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                    <h3 style="color: #155724; margin-top: 0;">✅ تم إنشاء حسابك بنجاح</h3>
                    <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{user_email}}</p>
                    <p style="margin: 5px 0;"><strong>تاريخ التسجيل:</strong> {{registration_date}}</p>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{app_url}}" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                        🚀 بدء الاستخدام
                    </a>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p><strong>شركة النسور الماسية للتجارة</strong></p>
                    <p>📧 {{support_email}} للدعم الفني</p>
                </div>
            </div>
        </div>
        `;
    }

    // Find user by email in system
    async findUserByEmail(email) {
        try {
            // Get users from localStorage
            const systemUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            
            // Find user by email (case insensitive)
            const user = systemUsers.find(u => 
                u.email && u.email.toLowerCase() === email.toLowerCase()
            );
            
            if (user) {
                return {
                    name: user.name || 'المستخدم',
                    email: user.email,
                    password: user.password || 'غير محدد'
                };
            }
            
            // Check default admin
            if (email.toLowerCase() === '<EMAIL>') {
                return {
                    name: 'المدير الرئيسي',
                    email: '<EMAIL>',
                    password: '2030'
                };
            }
            
            return null;
            
        } catch (error) {
            console.error('❌ خطأ في البحث عن المستخدم:', error);
            return null;
        }
    }

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Test email service
    async testEmailService() {
        try {
            console.log('🧪 اختبار خدمة البريد الإلكتروني...');
            
            const testResult = await this.sendPasswordRecovery('<EMAIL>', {
                name: 'مستخدم تجريبي',
                email: '<EMAIL>',
                password: 'test123'
            });
            
            return testResult;
            
        } catch (error) {
            console.error('❌ فشل اختبار خدمة البريد:', error);
            return { success: false, error: error };
        }
    }
}

// Create global instance
window.emailService = new EmailService();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailService;
}

console.log('📧 تم تحميل خدمة البريد الإلكتروني - النسور الماسية للتجارة');
