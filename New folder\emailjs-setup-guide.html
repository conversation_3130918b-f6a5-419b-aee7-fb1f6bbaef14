<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل إعداد EmailJS - النسور الماسية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .step h3 {
            margin-top: 0;
            color: #667eea;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: bold;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        ol {
            padding-right: 20px;
        }
        li {
            margin: 10px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="setup-icon">
                <i class="fas fa-cog"></i>
            </div>
            <h1>📧 دليل إعداد EmailJS</h1>
            <p><strong>لتفعيل خدمة إرسال كلمة المرور عبر البريد الإلكتروني</strong></p>
        </div>

        <div class="step">
            <h3>🚀 الخطوة 1: إنشاء حساب EmailJS</h3>
            <ol>
                <li>اذهب إلى <a href="https://www.emailjs.com" target="_blank">https://www.emailjs.com</a></li>
                <li>اضغط "Sign Up" لإنشاء حساب جديد</li>
                <li>أدخل بريدك الإلكتروني وكلمة مرور قوية</li>
                <li>تأكد من بريدك الإلكتروني</li>
            </ol>
            <a href="https://www.emailjs.com" target="_blank" class="btn">
                <i class="fas fa-external-link-alt"></i> فتح EmailJS
            </a>
        </div>

        <div class="step">
            <h3>📧 الخطوة 2: إعداد خدمة البريد الإلكتروني</h3>
            <ol>
                <li>في لوحة التحكم، اضغط "Add New Service"</li>
                <li>اختر "Gmail" (الأسهل والأكثر موثوقية)</li>
                <li>اتبع التعليمات لربط حساب Gmail الخاص بك</li>
                <li>احفظ <span class="highlight">Service ID</span> (مثل: service_abc123)</li>
            </ol>
            
            <div class="warning">
                <strong>⚠️ مهم:</strong> تأكد من تفعيل "Less secure app access" في Gmail أو استخدم App Password
            </div>
        </div>

        <div class="step">
            <h3>📝 الخطوة 3: إنشاء قالب البريد الإلكتروني</h3>
            <ol>
                <li>اضغط "Email Templates" ثم "Create New Template"</li>
                <li>اختر اسم للقالب (مثل: password_recovery)</li>
                <li>انسخ والصق القالب التالي:</li>
            </ol>
            
            <div class="code-block">
Subject: استرداد كلمة المرور - شركة النسور الماسية للتجارة

مرحباً {{user_name}},

تم طلب استرداد كلمة المرور لحسابك في نظام إدارة المخزون.

بيانات تسجيل الدخول:
البريد الإلكتروني: {{user_email}}
كلمة المرور: {{user_password}}

رابط التطبيق: {{app_url}}

تاريخ الطلب: {{recovery_date}} الساعة {{recovery_time}}

للدعم الفني: {{support_email}}

شركة النسور الماسية للتجارة
            </div>
            
            <ol start="4">
                <li>احفظ القالب واحفظ <span class="highlight">Template ID</span></li>
            </ol>
        </div>

        <div class="step">
            <h3>🔑 الخطوة 4: الحصول على Public Key</h3>
            <ol>
                <li>اذهب إلى "Account" في القائمة الجانبية</li>
                <li>انسخ <span class="highlight">Public Key</span> من قسم "API Keys"</li>
                <li>احفظ هذا المفتاح (مثل: user_abc123xyz)</li>
            </ol>
        </div>

        <div class="step">
            <h3>⚙️ الخطوة 5: تحديث إعدادات التطبيق</h3>
            <p>افتح ملف <code>email-service.js</code> وحدث الإعدادات التالية:</p>
            
            <div class="code-block">
this.serviceConfig = {
    emailjs: {
        publicKey: 'YOUR_PUBLIC_KEY_HERE',    // ضع Public Key هنا
        serviceId: 'YOUR_SERVICE_ID_HERE',    // ضع Service ID هنا
        templateId: 'YOUR_TEMPLATE_ID_HERE'   // ضع Template ID هنا
    }
};
            </div>
            
            <div class="warning">
                <strong>مثال:</strong><br>
                publicKey: 'user_abc123xyz'<br>
                serviceId: 'service_gmail123'<br>
                templateId: 'template_password_recovery'
            </div>
        </div>

        <div class="step">
            <h3>🧪 الخطوة 6: اختبار النظام</h3>
            <ol>
                <li>احفظ جميع الملفات</li>
                <li>افتح صفحة استرداد كلمة المرور</li>
                <li>أدخل بريد إلكتروني صحيح</li>
                <li>اضغط "إرسال كلمة المرور"</li>
                <li>تحقق من وصول البريد الإلكتروني</li>
            </ol>
            
            <a href="password-recovery.html" class="btn">
                <i class="fas fa-test-tube"></i> اختبار النظام
            </a>
        </div>

        <div class="success">
            <h4>✅ نصائح للنجاح:</h4>
            <ul>
                <li><strong>استخدم Gmail:</strong> الأكثر موثوقية مع EmailJS</li>
                <li><strong>تحقق من Spam:</strong> قد تصل الرسائل لصندوق الرسائل غير المرغوبة</li>
                <li><strong>اختبر أولاً:</strong> جرب النظام ببريدك الشخصي قبل النشر</li>
                <li><strong>احفظ النسخ الاحتياطية:</strong> احفظ جميع المفاتيح في مكان آمن</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔧 استكشاف الأخطاء وإصلاحها</h3>
            
            <h4>❌ مشكلة: "Invalid public key"</h4>
            <p><strong>الحل:</strong> تأكد من نسخ Public Key بشكل صحيح من حسابك في EmailJS</p>
            
            <h4>❌ مشكلة: "Service not found"</h4>
            <p><strong>الحل:</strong> تأكد من Service ID وأن الخدمة مفعلة في حسابك</p>
            
            <h4>❌ مشكلة: "Template not found"</h4>
            <p><strong>الحل:</strong> تأكد من Template ID وأن القالب محفوظ بشكل صحيح</p>
            
            <h4>❌ مشكلة: البريد لا يصل</h4>
            <p><strong>الحل:</strong> تحقق من صندوق الرسائل غير المرغوبة، وتأكد من إعدادات Gmail</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="btn" style="background: #6c757d;">
                <i class="fas fa-home"></i> العودة للتطبيق الرئيسي
            </a>
            <a href="password-recovery.html" class="btn">
                <i class="fas fa-key"></i> صفحة استرداد كلمة المرور
            </a>
        </div>
    </div>
</body>
</html>
