// Firebase Configuration for Diamond Eagles Store
// شركة النسور الماسية للتجارة - إعدادات Firebase

console.log('🔥 تحميل إعدادات Firebase...');

// Firebase Configuration
const firebaseConfig = {
    // ⚠️ يجب تحديث هذه الإعدادات من Firebase Console
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID"
};

// Initialize Firebase
let firebaseApp;
let db;
let isFirebaseReady = false;

try {
    // Initialize Firebase App
    firebaseApp = firebase.initializeApp(firebaseConfig);
    
    // Initialize Firestore
    db = firebase.firestore();
    
    // Enable offline persistence
    db.enablePersistence()
        .then(() => {
            console.log('✅ Firebase offline persistence enabled');
        })
        .catch((err) => {
            console.warn('⚠️ Firebase offline persistence failed:', err);
        });
    
    isFirebaseReady = true;
    console.log('✅ Firebase initialized successfully');
    
} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    isFirebaseReady = false;
}

// Firebase Service Class
class FirebaseService {
    constructor() {
        this.isReady = isFirebaseReady;
        this.db = db;
        this.collections = {
            products: 'products',
            customers: 'customers',
            users: 'users',
            settings: 'settings'
        };
    }

    // Check if Firebase is ready
    isFirebaseReady() {
        return this.isReady && this.db;
    }

    // Save products to Firebase
    async saveProducts(products) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.products).doc('data').set({
                products: products,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Products saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving products to Firebase:', error);
            return false;
        }
    }

    // Load products from Firebase
    async loadProducts() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.products).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Products loaded from Firebase');
                return data.products || [];
            } else {
                console.log('ℹ️ No products found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading products from Firebase:', error);
            return null;
        }
    }

    // Save customers to Firebase
    async saveCustomers(customers) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.customers).doc('data').set({
                customers: customers,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Customers saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving customers to Firebase:', error);
            return false;
        }
    }

    // Load customers from Firebase
    async loadCustomers() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.customers).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Customers loaded from Firebase');
                return data.customers || [];
            } else {
                console.log('ℹ️ No customers found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading customers from Firebase:', error);
            return null;
        }
    }

    // Save users to Firebase
    async saveUsers(users) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.users).doc('data').set({
                users: users,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Users saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving users to Firebase:', error);
            return false;
        }
    }

    // Load users from Firebase
    async loadUsers() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.users).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Users loaded from Firebase');
                return data.users || [];
            } else {
                console.log('ℹ️ No users found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading users from Firebase:', error);
            return null;
        }
    }

    // Sync all data to Firebase
    async syncAllToFirebase() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready for sync');
            return false;
        }

        try {
            console.log('🔄 Syncing all data to Firebase...');
            
            // Get data from localStorage
            const products = JSON.parse(localStorage.getItem('products') || '[]');
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            
            // Save to Firebase
            const results = await Promise.allSettled([
                this.saveProducts(products),
                this.saveCustomers(customers),
                this.saveUsers(users)
            ]);
            
            const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
            console.log(`✅ Synced ${successful}/3 collections to Firebase`);
            
            return successful === 3;
        } catch (error) {
            console.error('❌ Error syncing to Firebase:', error);
            return false;
        }
    }

    // Sync all data from Firebase
    async syncAllFromFirebase() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready for sync');
            return false;
        }

        try {
            console.log('🔄 Syncing all data from Firebase...');
            
            // Load from Firebase
            const [products, customers, users] = await Promise.all([
                this.loadProducts(),
                this.loadCustomers(),
                this.loadUsers()
            ]);
            
            // Save to localStorage
            if (products !== null) {
                localStorage.setItem('products', JSON.stringify(products));
                if (typeof window.products !== 'undefined') {
                    window.products = products;
                }
            }
            
            if (customers !== null) {
                localStorage.setItem('customers', JSON.stringify(customers));
                if (typeof window.customers !== 'undefined') {
                    window.customers = customers;
                }
            }
            
            if (users !== null) {
                localStorage.setItem('systemUsers', JSON.stringify(users));
            }
            
            console.log('✅ All data synced from Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error syncing from Firebase:', error);
            return false;
        }
    }

    // Get Firebase status
    getStatus() {
        return {
            isReady: this.isFirebaseReady(),
            hasConnection: this.isFirebaseReady(),
            collections: this.collections
        };
    }
}

// Create global Firebase service instance
window.firebaseService = new FirebaseService();

// Auto-sync functions
window.syncToFirebase = () => window.firebaseService.syncAllToFirebase();
window.syncFromFirebase = () => window.firebaseService.syncAllFromFirebase();

console.log('🔥 Firebase service ready:', window.firebaseService.getStatus());
