// Firebase Configuration for Web - DISABLED
// تكوين Firebase للويب - معطل

console.log('ℹ️ Firebase معطل - يتم استخدام Google Drive للتخزين السحابي');

// Firebase is disabled - using Google Drive instead
// Firebase معطل - يتم استخدام Google Drive بدلاً منه

// Dummy Firebase Manager to prevent errors
// مدير Firebase وهمي لمنع الأخطاء
class WebFirebaseManager {
    constructor() {
        this.isInitialized = false;
        this.disabled = true;
        this.currentUser = null;
        console.log('ℹ️ Firebase معطل - يتم استخدام Google Drive للتخزين السحابي');
    }

    async init() {
        console.log('ℹ️ Firebase معطل - يتم استخدام Google Drive للتخزين السحابي');
        this.isInitialized = false;
        return false;
    }

    async saveData(collection, docId, data) {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive للحفظ');
        return false;
    }

    async loadData(collection, docId) {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive للتحميل');
        return null;
    }

    async saveProducts(products) {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive لحفظ المنتجات');
        return false;
    }

    async loadProducts() {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive لتحميل المنتجات');
        return [];
    }

    async saveCustomers(customers) {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive لحفظ العملاء');
        return false;
    }

    async loadCustomers() {
        console.log('ℹ️ Firebase معطل - استخدم Google Drive لتحميل العملاء');
        return [];
    }

    isConnected() {
        return false;
    }

    getUserId() {
        return null;
    }
}

// Create global instance
const webFirebaseManager = new WebFirebaseManager();

// Export for global use
window.webFirebaseManager = webFirebaseManager;

// Dummy functions to prevent errors
// وظائف وهمية لمنع الأخطاء
window.saveToFirebase = function() {
    console.log('ℹ️ Firebase معطل - استخدم Google Drive للحفظ');
    return false;
};

window.loadFromFirebase = function() {
    console.log('ℹ️ Firebase معطل - استخدم Google Drive للتحميل');
    return null;
};

window.initializeFirebase = function() {
    console.log('ℹ️ Firebase معطل - يتم استخدام Google Drive للتخزين السحابي');
    return false;
};

// Update cloud UI to show Firebase is disabled
// تحديث واجهة السحابة لإظهار أن Firebase معطل
function updateFirebaseUI() {
    // Update any Firebase-related UI elements
    const firebaseElements = document.querySelectorAll('[id*="firebase"], [class*="firebase"]');
    firebaseElements.forEach(element => {
        if (element.textContent) {
            element.textContent = element.textContent.replace('Firebase', 'Google Drive');
        }
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        updateFirebaseUI();
    }, 1000);
});

console.log('✅ Firebase معطل - يتم استخدام Google Drive للتخزين السحابي');
