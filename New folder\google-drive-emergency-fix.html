<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح طارئ Google Drive - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .emergency-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .step h3 {
            margin-top: 0;
            color: #dc3545;
        }
        button {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
            font-weight: 500;
            width: 100%;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin: 15px 0;
            font-size: 12px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning-box {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="emergency-icon">
                🚨
            </div>
            <h1>🚨 إصلاح طارئ Google Drive</h1>
            <p><strong>حل جذري لمشكلة Auth Instance غير متاح</strong></p>
        </div>

        <div class="warning-box">
            <strong>⚠️ تحذير:</strong> هذا إصلاح طارئ سيعيد تعيين جميع إعدادات Google Drive ويبدأ من الصفر.
        </div>

        <div class="step">
            <h3>🔍 الخطوة 0: تشخيص مفصل</h3>
            <p>فحص مفصل لفهم سبب المشكلة بدقة</p>
            <button onclick="detailedDiagnosis()" class="btn-warning">🔍 تشخيص مفصل</button>
        </div>

        <div class="step">
            <h3>🔥 الخطوة 1: إصلاح جذري</h3>
            <p>مسح كامل وإعادة تحميل Google API من الصفر</p>
            <button onclick="emergencyReset()">🔥 إصلاح جذري</button>
        </div>

        <div class="step">
            <h3>🔧 الخطوة 2: تهيئة كاملة</h3>
            <p>تهيئة Google Drive API من البداية</p>
            <button onclick="fullInitialization()" class="btn-warning">🔧 تهيئة كاملة</button>
        </div>

        <div class="step">
            <h3>🔐 الخطوة 3: تسجيل دخول جديد</h3>
            <p>تسجيل دخول جديد تماماً</p>
            <button onclick="freshLogin()" class="btn-success">🔐 تسجيل دخول جديد</button>
        </div>

        <div class="step">
            <h3>✅ الخطوة 4: اختبار نهائي</h3>
            <p>اختبار شامل للتأكد من عمل كل شيء</p>
            <button onclick="finalTest()" class="btn-success">✅ اختبار نهائي</button>
        </div>

        <div id="status"></div>
        <div id="output"></div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="goToMainApp()" style="background: #6c757d; width: auto; padding: 10px 20px;">
                ↩️ العودة للتطبيق
            </button>
        </div>
    </div>

    <script>
        let currentStep = 0;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function detailedDiagnosis() {
            log('🔍 بدء التشخيص المفصل...');
            showStatus('🔍 جاري التشخيص المفصل...', 'info');

            try {
                log('=== تشخيص شامل لـ Google API ===');

                // 1. Check basic environment
                log('🌐 فحص البيئة الأساسية:');
                log(`- المتصفح: ${navigator.userAgent.split(' ').pop()}`);
                log(`- الرابط: ${window.location.href}`);
                log(`- البروتوكول: ${window.location.protocol}`);

                // 2. Check window object
                log('🪟 فحص كائن النافذة:');
                log(`- window: ${typeof window}`);
                log(`- document: ${typeof document}`);

                // 3. Check gapi
                log('📦 فحص Google API:');
                log(`- typeof gapi: ${typeof gapi}`);

                if (typeof gapi !== 'undefined') {
                    log(`- gapi.load: ${typeof gapi.load}`);
                    log(`- gapi.auth2: ${typeof gapi.auth2}`);
                    log(`- gapi.client: ${typeof gapi.client}`);

                    if (gapi.auth2) {
                        log(`- gapi.auth2.init: ${typeof gapi.auth2.init}`);
                        log(`- gapi.auth2.getAuthInstance: ${typeof gapi.auth2.getAuthInstance}`);

                        try {
                            const authInstance = gapi.auth2.getAuthInstance();
                            log(`- getAuthInstance() result: ${authInstance ? 'كائن صالح' : 'null/undefined'}`);

                            if (authInstance) {
                                log(`- authInstance.signIn: ${typeof authInstance.signIn}`);
                                log(`- authInstance.isSignedIn: ${typeof authInstance.isSignedIn}`);
                                if (authInstance.isSignedIn) {
                                    log(`- authInstance.isSignedIn.get: ${typeof authInstance.isSignedIn.get}`);
                                    try {
                                        const isSignedIn = authInstance.isSignedIn.get();
                                        log(`- حالة تسجيل الدخول: ${isSignedIn ? 'مسجل' : 'غير مسجل'}`);
                                    } catch (e) {
                                        log(`- خطأ في فحص حالة تسجيل الدخول: ${e.message}`);
                                    }
                                }
                            }
                        } catch (e) {
                            log(`- خطأ في getAuthInstance: ${e.message}`);
                        }
                    }
                } else {
                    log('❌ gapi غير متاح');
                }

                // 4. Check scripts
                log('📜 فحص Scripts المحملة:');
                const scripts = document.querySelectorAll('script');
                let googleScripts = 0;
                scripts.forEach((script, index) => {
                    if (script.src && (script.src.includes('google') || script.src.includes('gapi'))) {
                        log(`- Script ${index}: ${script.src}`);
                        googleScripts++;
                    }
                });
                log(`- عدد Google Scripts: ${googleScripts}`);

                // 5. Check localStorage
                log('💾 فحص التخزين المحلي:');
                try {
                    const keys = Object.keys(localStorage);
                    const googleKeys = keys.filter(key => key.toLowerCase().includes('google'));
                    log(`- مفاتيح Google في localStorage: ${googleKeys.length}`);
                    googleKeys.forEach(key => {
                        log(`  - ${key}: ${localStorage.getItem(key)?.substring(0, 50)}...`);
                    });
                } catch (e) {
                    log(`- خطأ في فحص localStorage: ${e.message}`);
                }

                // 6. Check global variables
                log('🌍 فحص المتغيرات العامة:');
                log(`- window.isGoogleDriveConnected: ${window.isGoogleDriveConnected}`);
                log(`- window.isGoogleDriveReady: ${window.isGoogleDriveReady}`);
                log(`- window.googleDriveUser: ${window.googleDriveUser ? 'موجود' : 'غير موجود'}`);

                // 7. Network check
                log('🌐 فحص الشبكة:');
                log(`- navigator.onLine: ${navigator.onLine}`);

                log('=== انتهى التشخيص ===');
                showStatus('✅ تم إكمال التشخيص المفصل - راجع السجل', 'success');

            } catch (error) {
                log(`❌ خطأ في التشخيص: ${error.message}`);
                showStatus('❌ خطأ في التشخيص', 'error');
            }
        }

        async function emergencyReset() {
            currentStep = 1;
            log('🔥 بدء الإصلاح الجذري...');
            showStatus('🔥 جاري الإصلاح الجذري...', 'error');

            try {
                // 1. Clear all Google-related variables
                log('🗑️ مسح جميع متغيرات Google...');
                if (typeof gapi !== 'undefined') {
                    delete window.gapi;
                }
                window.isGoogleDriveConnected = false;
                window.isGoogleDriveReady = false;
                window.googleDriveUser = null;

                // 2. Remove all Google scripts
                log('🗑️ حذف جميع scripts Google...');
                const googleScripts = document.querySelectorAll('script[src*="google"], script[src*="gapi"], script[src*="apis.google"]');
                googleScripts.forEach(script => script.remove());

                // 3. Clear localStorage
                log('🗑️ مسح التخزين المحلي...');
                localStorage.removeItem('googleDriveAppFolderId');
                localStorage.removeItem('lastGoogleDriveSync');
                localStorage.removeItem('googleDriveConnected');

                // 4. Clear sessionStorage
                log('🗑️ مسح تخزين الجلسة...');
                sessionStorage.clear();

                // 5. Wait a moment
                await new Promise(resolve => setTimeout(resolve, 2000));

                log('✅ تم الإصلاح الجذري بنجاح');
                showStatus('✅ تم الإصلاح الجذري - انتقل للخطوة التالية', 'success');

            } catch (error) {
                log(`❌ خطأ في الإصلاح الجذري: ${error.message}`);
                showStatus('❌ فشل الإصلاح الجذري', 'error');
            }
        }

        async function fullInitialization() {
            if (currentStep < 1) {
                showStatus('⚠️ يجب تنفيذ الإصلاح الجذري أولاً', 'error');
                return;
            }

            currentStep = 2;
            log('🔧 بدء التهيئة الكاملة...');
            showStatus('🔧 جاري التهيئة الكاملة...', 'info');

            try {
                // 1. Load Google API fresh
                log('📥 تحميل Google API جديد...');
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://apis.google.com/js/api.js?v=' + Date.now();
                    script.async = true;
                    script.defer = true;
                    
                    script.onload = () => {
                        log('✅ تم تحميل Google API');
                        resolve();
                    };
                    
                    script.onerror = () => {
                        log('❌ فشل في تحميل Google API');
                        reject(new Error('فشل في تحميل Google API'));
                    };
                    
                    document.head.appendChild(script);
                });

                // 2. Wait for gapi to be available
                log('⏳ انتظار توفر gapi...');
                let attempts = 0;
                while (typeof gapi === 'undefined' && attempts < 100) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (typeof gapi === 'undefined') {
                    throw new Error('gapi غير متاح بعد المحاولات');
                }

                log('✅ gapi متاح الآن');

                // 3. Load auth2
                log('📥 تحميل Auth2...');
                await new Promise((resolve, reject) => {
                    gapi.load('auth2', {
                        callback: () => {
                            log('✅ تم تحميل Auth2');
                            resolve();
                        },
                        onerror: () => {
                            log('❌ فشل في تحميل Auth2');
                            reject(new Error('فشل في تحميل Auth2'));
                        }
                    });
                });

                // 4. Initialize auth2 with detailed logging
                log('🔧 تهيئة Auth2...');
                log('📋 معرف العميل: 176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com');

                try {
                    const authInstance = await gapi.auth2.init({
                        client_id: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                        scope: 'https://www.googleapis.com/auth/drive.file'
                    });

                    log('✅ تم تهيئة Auth2 بنجاح');
                    log(`📊 Auth Instance ID: ${authInstance ? 'موجود' : 'غير موجود'}`);

                    // Double check
                    const checkInstance = gapi.auth2.getAuthInstance();
                    log(`🔍 فحص Auth Instance: ${checkInstance ? 'متاح' : 'غير متاح'}`);

                    if (!checkInstance) {
                        log('⚠️ Auth Instance غير متاح بعد التهيئة - محاولة إصلاح...');

                        // Try alternative initialization
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        const retryInstance = gapi.auth2.getAuthInstance();

                        if (!retryInstance) {
                            throw new Error('فشل في إنشاء Auth Instance حتى بعد المحاولة الثانية');
                        }

                        log('✅ تم إنشاء Auth Instance في المحاولة الثانية');
                    }

                } catch (authError) {
                    log(`❌ خطأ في تهيئة Auth2: ${authError.message}`);
                    throw authError;
                }

                // 5. Load client
                log('📥 تحميل Client...');
                await new Promise((resolve, reject) => {
                    gapi.load('client', {
                        callback: () => {
                            log('✅ تم تحميل Client');
                            resolve();
                        },
                        onerror: () => {
                            log('❌ فشل في تحميل Client');
                            reject(new Error('فشل في تحميل Client'));
                        }
                    });
                });

                // 6. Initialize client
                log('🔧 تهيئة Client...');
                await gapi.client.init({
                    apiKey: 'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8',
                    clientId: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                    discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
                    scope: 'https://www.googleapis.com/auth/drive.file'
                });

                log('✅ تم تهيئة Client');

                // 7. Final verification with detailed checks
                log('🔍 فحص نهائي شامل...');

                // Check gapi.auth2
                if (!gapi.auth2) {
                    throw new Error('gapi.auth2 غير متاح');
                }
                log('✅ gapi.auth2 متاح');

                // Check getAuthInstance function
                if (typeof gapi.auth2.getAuthInstance !== 'function') {
                    throw new Error('gapi.auth2.getAuthInstance ليس دالة');
                }
                log('✅ gapi.auth2.getAuthInstance متاح');

                // Get auth instance
                const authInstance = gapi.auth2.getAuthInstance();
                log(`🔍 نتيجة getAuthInstance: ${authInstance ? 'كائن صالح' : 'null/undefined'}`);

                if (!authInstance) {
                    // Try to get more info
                    log('🔍 معلومات إضافية:');
                    log(`- gapi: ${typeof gapi}`);
                    log(`- gapi.auth2: ${typeof gapi.auth2}`);
                    log(`- gapi.auth2.getAuthInstance: ${typeof gapi.auth2.getAuthInstance}`);

                    throw new Error('Auth Instance ما زال غير متاح بعد جميع المحاولات');
                }

                // Check auth instance methods
                if (typeof authInstance.isSignedIn !== 'object' || typeof authInstance.isSignedIn.get !== 'function') {
                    throw new Error('Auth Instance لا يحتوي على الطرق المطلوبة');
                }

                log('✅ Auth Instance متاح ويحتوي على جميع الطرق المطلوبة!');
                showStatus('✅ تم إكمال التهيئة الكاملة بنجاح!', 'success');

            } catch (error) {
                log(`❌ خطأ في التهيئة الكاملة: ${error.message}`);
                showStatus('❌ فشل في التهيئة الكاملة', 'error');
            }
        }

        async function freshLogin() {
            if (currentStep < 2) {
                showStatus('⚠️ يجب إكمال التهيئة الكاملة أولاً', 'error');
                return;
            }

            currentStep = 3;
            log('🔐 بدء تسجيل دخول جديد...');
            showStatus('🔐 جاري تسجيل الدخول...', 'info');

            try {
                // Detailed pre-login checks
                log('🔍 فحص ما قبل تسجيل الدخول...');

                // Check gapi
                if (typeof gapi === 'undefined') {
                    throw new Error('gapi غير متاح');
                }
                log('✅ gapi متاح');

                // Check gapi.auth2
                if (!gapi.auth2) {
                    throw new Error('gapi.auth2 غير متاح');
                }
                log('✅ gapi.auth2 متاح');

                // Check getAuthInstance function
                if (typeof gapi.auth2.getAuthInstance !== 'function') {
                    throw new Error('gapi.auth2.getAuthInstance ليس دالة');
                }
                log('✅ gapi.auth2.getAuthInstance متاح');

                // Get auth instance with detailed logging
                log('🔍 محاولة الحصول على Auth Instance...');
                const authInstance = gapi.auth2.getAuthInstance();

                log(`📊 نتيجة getAuthInstance: ${authInstance ? 'كائن صالح' : 'null/undefined'}`);

                if (!authInstance) {
                    log('❌ Auth Instance غير متاح');
                    log('🔍 معلومات تشخيصية:');
                    log(`- typeof gapi: ${typeof gapi}`);
                    log(`- typeof gapi.auth2: ${typeof gapi.auth2}`);
                    log(`- typeof gapi.auth2.getAuthInstance: ${typeof gapi.auth2.getAuthInstance}`);

                    // Try to re-initialize
                    log('🔄 محاولة إعادة التهيئة...');
                    try {
                        await gapi.auth2.init({
                            client_id: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                            scope: 'https://www.googleapis.com/auth/drive.file'
                        });

                        const retryInstance = gapi.auth2.getAuthInstance();
                        if (!retryInstance) {
                            throw new Error('فشل في إعادة التهيئة');
                        }

                        log('✅ تم إنشاء Auth Instance بعد إعادة التهيئة');
                        authInstance = retryInstance;

                    } catch (reinitError) {
                        throw new Error(`Auth Instance غير متاح حتى بعد إعادة التهيئة: ${reinitError.message}`);
                    }
                }

                log('✅ Auth Instance متاح');

                // Check auth instance methods
                if (typeof authInstance.signIn !== 'function') {
                    throw new Error('Auth Instance لا يحتوي على دالة signIn');
                }
                log('✅ دالة signIn متاحة');

                if (typeof authInstance.isSignedIn !== 'object' || typeof authInstance.isSignedIn.get !== 'function') {
                    throw new Error('Auth Instance لا يحتوي على isSignedIn.get');
                }
                log('✅ دالة isSignedIn.get متاحة');

                // Attempt sign in
                log('🔐 محاولة تسجيل الدخول...');
                const user = await authInstance.signIn({
                    prompt: 'select_account'
                });

                log(`📊 نتيجة signIn: ${user ? 'كائن مستخدم' : 'null/undefined'}`);

                if (user && typeof user.isSignedIn === 'function' && user.isSignedIn()) {
                    const profile = user.getBasicProfile();
                    log(`✅ تم تسجيل الدخول: ${profile.getName()}`);
                    log(`📧 البريد: ${profile.getEmail()}`);

                    // Update global states
                    window.isGoogleDriveConnected = true;
                    window.isGoogleDriveReady = true;
                    window.googleDriveUser = profile;

                    showStatus(`✅ تم تسجيل الدخول بنجاح!`, 'success');
                } else {
                    log('❌ المستخدم غير مسجل دخول بعد المحاولة');
                    throw new Error('فشل في تسجيل الدخول - المستخدم غير مسجل');
                }

            } catch (error) {
                log(`❌ خطأ في تسجيل الدخول: ${error.message}`);

                if (error.error === 'popup_blocked_by_browser') {
                    showStatus('❌ تم حظر النافذة المنبثقة - اسمح بالنوافذ المنبثقة وأعد المحاولة', 'error');
                } else if (error.error === 'access_denied') {
                    showStatus('❌ تم رفض الوصول - وافق على الأذونات وأعد المحاولة', 'error');
                } else if (error.message.includes('Auth Instance غير متاح')) {
                    showStatus('❌ مشكلة في Auth Instance - أعد التهيئة الكاملة', 'error');
                } else {
                    showStatus(`❌ فشل في تسجيل الدخول: ${error.message}`, 'error');
                }
            }
        }

        async function finalTest() {
            if (currentStep < 3) {
                showStatus('⚠️ يجب إكمال تسجيل الدخول أولاً', 'error');
                return;
            }

            log('✅ بدء الاختبار النهائي...');
            showStatus('✅ جاري الاختبار النهائي...', 'info');

            try {
                // Test 1: Check gapi
                if (typeof gapi === 'undefined') {
                    throw new Error('gapi غير متاح');
                }
                log('✅ gapi متاح');

                // Test 2: Check auth2
                if (!gapi.auth2) {
                    throw new Error('auth2 غير متاح');
                }
                log('✅ auth2 متاح');

                // Test 3: Check auth instance
                const authInstance = gapi.auth2.getAuthInstance();
                if (!authInstance) {
                    throw new Error('Auth Instance غير متاح');
                }
                log('✅ Auth Instance متاح');

                // Test 4: Check signed in
                if (!authInstance.isSignedIn.get()) {
                    throw new Error('غير مسجل دخول');
                }
                log('✅ مسجل دخول');

                // Test 5: Check client
                if (!gapi.client || !gapi.client.drive) {
                    throw new Error('Drive Client غير متاح');
                }
                log('✅ Drive Client متاح');

                // Test 6: Test API call
                log('🧪 اختبار استدعاء API...');
                const response = await gapi.client.drive.about.get({
                    fields: 'user'
                });
                
                if (response && response.result) {
                    log('✅ API يعمل بشكل صحيح');
                    log(`👤 المستخدم: ${response.result.user.displayName}`);
                } else {
                    throw new Error('فشل في استدعاء API');
                }

                showStatus('🎉 تم الإصلاح بنجاح! Google Drive يعمل الآن بشكل كامل', 'success');
                log('🎉 جميع الاختبارات نجحت - Google Drive جاهز للاستخدام!');

            } catch (error) {
                log(`❌ فشل في الاختبار النهائي: ${error.message}`);
                showStatus('❌ فشل في الاختبار النهائي', 'error');
            }
        }

        function goToMainApp() {
            if (confirm('هل تريد العودة للتطبيق الرئيسي؟\nتأكد من إكمال جميع الخطوات أولاً.')) {
                window.location.href = 'index.html';
            }
        }

        // Auto-start message
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚨 أداة الإصلاح الطارئ جاهزة');
                showStatus('ابدأ بالخطوة 1: الإصلاح الجذري', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
