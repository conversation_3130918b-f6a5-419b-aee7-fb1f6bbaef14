<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح Google Drive API - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4285f4;
        }
        .step h3 {
            margin-top: 0;
            color: #4285f4;
        }
        button {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
            font-weight: 500;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin: 15px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4285f4, #34a853);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fab fa-google-drive"></i>
            </div>
            <h1>🔧 إصلاح Google Drive API</h1>
            <p>أداة شاملة لتشخيص وإصلاح مشاكل Google Drive</p>
        </div>

        <div class="step">
            <h3>📊 الخطوة 1: فحص الحالة الحالية</h3>
            <p>فحص شامل لحالة Google Drive API والاتصال</p>
            <button onclick="checkCurrentStatus()">🔍 فحص الحالة</button>
            <button onclick="clearOutput()">🗑️ مسح النتائج</button>
        </div>

        <div class="step">
            <h3>🔧 الخطوة 2: إصلاح المشاكل</h3>
            <p>إصلاح تلقائي للمشاكل الشائعة</p>
            <button onclick="fixGoogleDriveIssues()" class="btn-warning">🛠️ إصلاح تلقائي</button>
            <button onclick="forceReloadGoogleAPI()" class="btn-warning">🔄 إعادة تحميل API</button>
            <button onclick="resetGoogleDrive()" class="btn-danger">🔄 إعادة تعيين كاملة</button>
        </div>

        <div class="step">
            <h3>🔗 الخطوة 3: إعادة الاتصال</h3>
            <p>تسجيل دخول جديد إلى Google Drive</p>
            <button onclick="manualGoogleDriveInit()" class="btn-success">🔧 تهيئة يدوية</button>
            <button onclick="reconnectGoogleDrive()" class="btn-success">🔐 إعادة تسجيل الدخول</button>
            <button onclick="testConnection()" class="btn-success">✅ اختبار الاتصال</button>
        </div>

        <div class="step">
            <h3>📤 الخطوة 4: اختبار المزامنة</h3>
            <p>اختبار رفع وتحميل البيانات</p>
            <button onclick="testUpload()">📤 اختبار الرفع</button>
            <button onclick="testDownload()">📥 اختبار التحميل</button>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="status"></div>
        <div id="output"></div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="goToMainApp()" style="background: #6c757d;">
                ↩️ العودة للتطبيق الرئيسي
            </button>
            <button onclick="goToAPIManagement()" style="background: #17a2b8;">
                🔧 إدارة APIs
            </button>
        </div>
    </div>

    <!-- Load Google Drive System -->
    <script src="google-drive-sync.js"></script>
    <script src="api-integration.js"></script>

    <script>
        let currentStep = 0;
        const totalSteps = 4;

        function updateProgress(step) {
            currentStep = step;
            const percentage = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            log('تم مسح النتائج');
        }

        async function checkCurrentStatus() {
            updateProgress(1);
            log('🔍 بدء فحص حالة Google Drive API...');
            showStatus('جاري فحص الحالة...', 'info');

            try {
                // Check if Google API is loaded
                if (typeof gapi === 'undefined') {
                    log('❌ Google API غير محمل');
                    showStatus('❌ Google API غير محمل - يحتاج إعادة تحميل', 'error');
                    return false;
                }
                log('✅ Google API محمل');

                // Check if auth2 is initialized
                if (!gapi.auth2) {
                    log('⚠️ Google Auth2 غير مهيأ');
                    showStatus('⚠️ Google Auth2 غير مهيأ - يحتاج تهيئة', 'warning');
                    return false;
                }
                log('✅ Google Auth2 مهيأ');

                // Check auth instance
                const authInstance = gapi.auth2.getAuthInstance();
                if (!authInstance) {
                    log('❌ Auth Instance غير متاح');
                    showStatus('❌ Auth Instance غير متاح', 'error');
                    return false;
                }
                log('✅ Auth Instance متاح');

                // Check if signed in
                if (authInstance.isSignedIn.get()) {
                    const user = authInstance.currentUser.get();
                    const profile = user.getBasicProfile();
                    log(`✅ مسجل دخول: ${profile.getName()}`);
                    log(`📧 البريد: ${profile.getEmail()}`);
                    showStatus(`✅ متصل بـ Google Drive - ${profile.getName()}`, 'success');
                    
                    // Update global status
                    window.isGoogleDriveConnected = true;
                    window.isGoogleDriveReady = true;
                    window.googleDriveUser = profile;
                    
                    return true;
                } else {
                    log('⚠️ غير مسجل دخول');
                    showStatus('⚠️ غير مسجل دخول - يحتاج تسجيل دخول', 'warning');
                    return false;
                }

            } catch (error) {
                log(`❌ خطأ في الفحص: ${error.message}`);
                showStatus(`❌ خطأ في الفحص: ${error.message}`, 'error');
                return false;
            }
        }

        async function fixGoogleDriveIssues() {
            updateProgress(2);
            log('🛠️ بدء الإصلاح التلقائي...');
            showStatus('جاري الإصلاح...', 'info');

            try {
                // Step 1: Reset states
                window.isGoogleDriveConnected = false;
                window.isGoogleDriveReady = false;
                window.googleDriveUser = null;
                log('🔄 تم إعادة تعيين الحالات');

                // Step 2: Manual Google API initialization
                if (typeof gapi !== 'undefined') {
                    log('🔄 تهيئة Google API يدوياً...');

                    // Load auth2 if not loaded
                    if (!gapi.auth2) {
                        log('📥 تحميل Google Auth2...');
                        await new Promise((resolve, reject) => {
                            gapi.load('auth2', {
                                callback: resolve,
                                onerror: reject
                            });
                        });
                        log('✅ تم تحميل Google Auth2');
                    }

                    // Initialize auth2 if not initialized
                    if (!gapi.auth2.getAuthInstance()) {
                        log('🔧 تهيئة Auth Instance...');
                        await gapi.auth2.init({
                            client_id: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                            scope: 'https://www.googleapis.com/auth/drive.file'
                        });
                        log('✅ تم تهيئة Auth Instance');
                    }

                    // Load client API
                    if (!gapi.client) {
                        log('📥 تحميل Google Client API...');
                        await new Promise((resolve, reject) => {
                            gapi.load('client', {
                                callback: resolve,
                                onerror: reject
                            });
                        });
                        log('✅ تم تحميل Google Client API');
                    }

                    // Initialize client
                    if (!gapi.client.drive) {
                        log('🔧 تهيئة Drive Client...');
                        await gapi.client.init({
                            apiKey: 'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8',
                            clientId: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                            discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
                            scope: 'https://www.googleapis.com/auth/drive.file'
                        });
                        log('✅ تم تهيئة Drive Client');
                    }

                } else {
                    log('❌ Google API غير محمل - إعادة تحميل الصفحة مطلوب');
                    showStatus('❌ Google API غير محمل - أعد تحميل الصفحة', 'error');
                    return;
                }

                // Step 3: Try existing initialization functions
                if (typeof initializeGoogleDriveSync === 'function') {
                    log('🔄 تشغيل تهيئة Google Drive Sync...');
                    await initializeGoogleDriveSync();
                    log('✅ تم تشغيل Google Drive Sync');
                } else if (typeof initializeGoogleDrive === 'function') {
                    log('🔄 تشغيل تهيئة Google Drive...');
                    await initializeGoogleDrive();
                    log('✅ تم تشغيل Google Drive');
                }

                // Step 4: Check status again
                const isFixed = await checkCurrentStatus();

                if (isFixed) {
                    showStatus('✅ تم الإصلاح بنجاح!', 'success');
                } else {
                    showStatus('⚠️ الإصلاح جزئي - جرب إعادة تسجيل الدخول', 'warning');
                }

            } catch (error) {
                log(`❌ خطأ في الإصلاح: ${error.message}`);
                showStatus(`❌ فشل الإصلاح: ${error.message}`, 'error');

                // Suggest manual reload
                log('💡 جرب إعادة تحميل الصفحة ثم المحاولة مرة أخرى');
            }
        }

        async function forceReloadGoogleAPI() {
            log('🔄 إعادة تحميل Google API من الصفر...');
            showStatus('جاري إعادة تحميل Google API...', 'info');

            try {
                // Remove existing Google API script
                const existingScript = document.querySelector('script[src*="apis.google.com"]');
                if (existingScript) {
                    existingScript.remove();
                    log('🗑️ تم حذف Google API القديم');
                }

                // Reset gapi
                if (typeof gapi !== 'undefined') {
                    delete window.gapi;
                    log('🗑️ تم حذف gapi من الذاكرة');
                }

                // Load Google API fresh
                log('📥 تحميل Google API جديد...');
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://apis.google.com/js/api.js';
                    script.onload = () => {
                        log('✅ تم تحميل Google API');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل Google API');
                        reject(new Error('فشل في تحميل Google API'));
                    };
                    document.head.appendChild(script);
                });

                // Wait for gapi to be available
                let attempts = 0;
                while (typeof gapi === 'undefined' && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (typeof gapi === 'undefined') {
                    throw new Error('Google API لم يتم تحميله بعد المحاولات');
                }

                log('✅ Google API متاح الآن');
                showStatus('✅ تم إعادة تحميل Google API - جرب الإصلاح التلقائي', 'success');

            } catch (error) {
                log(`❌ خطأ في إعادة تحميل Google API: ${error.message}`);
                showStatus(`❌ فشل في إعادة تحميل Google API`, 'error');
            }
        }

        async function resetGoogleDrive() {
            if (!confirm('هل أنت متأكد من إعادة التعيين الكاملة؟\nسيتم قطع الاتصال وحذف جميع البيانات المحفوظة.')) {
                return;
            }

            updateProgress(2);
            log('🔄 بدء إعادة التعيين الكاملة...');
            showStatus('جاري إعادة التعيين...', 'info');

            try {
                // Sign out if signed in
                if (typeof gapi !== 'undefined' && gapi.auth2) {
                    const authInstance = gapi.auth2.getAuthInstance();
                    if (authInstance && authInstance.isSignedIn.get()) {
                        await authInstance.signOut();
                        log('🚪 تم تسجيل الخروج');
                    }
                }

                // Reset all states
                window.isGoogleDriveConnected = false;
                window.isGoogleDriveReady = false;
                window.googleDriveUser = null;

                // Clear localStorage
                localStorage.removeItem('googleDriveAppFolderId');
                localStorage.removeItem('lastGoogleDriveSync');

                log('✅ تم إعادة التعيين الكاملة');
                showStatus('✅ تم إعادة التعيين - يمكنك الآن إعادة الاتصال', 'success');

            } catch (error) {
                log(`❌ خطأ في إعادة التعيين: ${error.message}`);
                showStatus(`❌ فشل إعادة التعيين: ${error.message}`, 'error');
            }
        }

        async function manualGoogleDriveInit() {
            log('🔧 بدء التهيئة اليدوية لـ Google Drive...');
            showStatus('جاري التهيئة اليدوية...', 'info');

            try {
                // Check if gapi is available
                if (typeof gapi === 'undefined') {
                    log('❌ Google API غير متاح - استخدم "إعادة تحميل API" أولاً');
                    showStatus('❌ Google API غير متاح', 'error');
                    return;
                }

                log('✅ Google API متاح');

                // Load auth2 module
                log('📥 تحميل وحدة Auth2...');
                await new Promise((resolve, reject) => {
                    gapi.load('auth2', {
                        callback: () => {
                            log('✅ تم تحميل وحدة Auth2');
                            resolve();
                        },
                        onerror: (error) => {
                            log('❌ فشل في تحميل وحدة Auth2');
                            reject(error);
                        }
                    });
                });

                // Initialize auth2
                log('🔧 تهيئة Auth2...');
                const authInstance = await gapi.auth2.init({
                    client_id: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                    scope: 'https://www.googleapis.com/auth/drive.file'
                });

                log('✅ تم تهيئة Auth2 بنجاح');

                // Load client module
                log('📥 تحميل وحدة Client...');
                await new Promise((resolve, reject) => {
                    gapi.load('client', {
                        callback: () => {
                            log('✅ تم تحميل وحدة Client');
                            resolve();
                        },
                        onerror: (error) => {
                            log('❌ فشل في تحميل وحدة Client');
                            reject(error);
                        }
                    });
                });

                // Initialize client
                log('🔧 تهيئة Client...');
                await gapi.client.init({
                    apiKey: 'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8',
                    clientId: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
                    discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
                    scope: 'https://www.googleapis.com/auth/drive.file'
                });

                log('✅ تم تهيئة Client بنجاح');

                // Update global states
                window.isGoogleDriveReady = true;
                log('✅ تم تحديث الحالة العامة');

                showStatus('✅ تم إكمال التهيئة اليدوية بنجاح!', 'success');

                // Test the connection
                setTimeout(() => {
                    checkCurrentStatus();
                }, 1000);

            } catch (error) {
                log(`❌ خطأ في التهيئة اليدوية: ${error.message}`);
                showStatus(`❌ فشل في التهيئة اليدوية`, 'error');
            }
        }

        async function reconnectGoogleDrive() {
            updateProgress(3);
            log('🔐 بدء إعادة تسجيل الدخول...');
            showStatus('جاري تسجيل الدخول...', 'info');

            try {
                // Check if auth instance is available
                if (typeof gapi === 'undefined' || !gapi.auth2) {
                    log('❌ Google Auth غير متاح - جرب التهيئة اليدوية أولاً');
                    showStatus('❌ Google Auth غير متاح', 'error');
                    return;
                }

                const authInstance = gapi.auth2.getAuthInstance();
                if (!authInstance) {
                    log('❌ Auth Instance غير متاح - جرب التهيئة اليدوية أولاً');
                    showStatus('❌ Auth Instance غير متاح', 'error');
                    return;
                }

                // Try to sign in
                log('🔐 محاولة تسجيل الدخول...');
                const user = await authInstance.signIn({
                    prompt: 'select_account'
                });

                if (user && user.isSignedIn()) {
                    const profile = user.getBasicProfile();
                    log(`✅ تم تسجيل الدخول: ${profile.getName()}`);
                    log(`📧 البريد: ${profile.getEmail()}`);

                    // Update global states
                    window.isGoogleDriveConnected = true;
                    window.isGoogleDriveReady = true;
                    window.googleDriveUser = profile;

                    showStatus(`✅ تم تسجيل الدخول بنجاح - ${profile.getName()}`, 'success');
                    await checkCurrentStatus();
                } else {
                    log('❌ فشل في تسجيل الدخول');
                    showStatus('❌ فشل في تسجيل الدخول', 'error');
                }

                // Try using existing function as fallback
                if (typeof connectToGoogleDrive === 'function') {
                    log('🔄 محاولة استخدام الوظيفة الموجودة...');
                    const result = await connectToGoogleDrive();
                    if (result) {
                        log('✅ نجح الاتصال باستخدام الوظيفة الموجودة');
                    }
                }

            } catch (error) {
                log(`❌ خطأ في تسجيل الدخول: ${error.message}`);

                if (error.error === 'popup_blocked_by_browser') {
                    showStatus('❌ تم حظر النافذة المنبثقة - اسمح بالنوافذ المنبثقة', 'error');
                } else if (error.error === 'access_denied') {
                    showStatus('❌ تم رفض الوصول - وافق على الأذونات', 'error');
                } else {
                    showStatus(`❌ خطأ في تسجيل الدخول`, 'error');
                }
            }
        }

        async function testConnection() {
            log('🔗 اختبار الاتصال...');
            const isConnected = await checkCurrentStatus();
            
            if (isConnected) {
                log('✅ الاتصال يعمل بشكل صحيح');
                showStatus('✅ الاتصال يعمل بشكل صحيح', 'success');
            } else {
                log('❌ الاتصال لا يعمل');
                showStatus('❌ الاتصال لا يعمل - جرب الإصلاح', 'error');
            }
        }

        async function testUpload() {
            updateProgress(4);
            log('📤 اختبار رفع البيانات...');
            
            try {
                if (typeof uploadProductsToDrive === 'function') {
                    const result = await uploadProductsToDrive();
                    if (result) {
                        log('✅ تم رفع البيانات بنجاح');
                        showStatus('✅ اختبار الرفع نجح', 'success');
                    } else {
                        log('❌ فشل في رفع البيانات');
                        showStatus('❌ فشل اختبار الرفع', 'error');
                    }
                } else {
                    log('⚠️ وظيفة الرفع غير متاحة');
                    showStatus('⚠️ وظيفة الرفع غير متاحة', 'warning');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الرفع: ${error.message}`);
                showStatus(`❌ خطأ في اختبار الرفع`, 'error');
            }
        }

        async function testDownload() {
            log('📥 اختبار تحميل البيانات...');
            
            try {
                if (typeof downloadProductsFromDrive === 'function') {
                    await downloadProductsFromDrive();
                    log('✅ تم تحميل البيانات بنجاح');
                    showStatus('✅ اختبار التحميل نجح', 'success');
                } else {
                    log('⚠️ وظيفة التحميل غير متاحة');
                    showStatus('⚠️ وظيفة التحميل غير متاحة', 'warning');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التحميل: ${error.message}`);
                showStatus(`❌ خطأ في اختبار التحميل`, 'error');
            }
        }

        function goToMainApp() {
            window.location.href = 'index.html';
        }

        function goToAPIManagement() {
            window.location.href = 'api-management.html';
        }

        // Auto-check status on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 تم تحميل أداة إصلاح Google Drive');
                showStatus('مرحباً! اضغط "فحص الحالة" للبدء', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
