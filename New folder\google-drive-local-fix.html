<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حل مشكلة Google Drive للملفات المحلية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        .solution {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .solution h3 {
            margin-top: 0;
            color: #155724;
        }
        .problem-box {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .code-box {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
            font-weight: 500;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .step h4 {
            margin-top: 0;
            color: #28a745;
        }
        ol {
            padding-right: 20px;
        }
        li {
            margin: 10px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">
                ✅
            </div>
            <h1>🎯 حل مشكلة Google Drive</h1>
            <p><strong>المشكلة محددة والحل جاهز!</strong></p>
        </div>

        <div class="problem-box">
            <h3>🔍 المشكلة المكتشفة:</h3>
            <p><strong>خطأ: Invalid cookiePolicy</strong></p>
            <p><strong>السبب:</strong> Google API لا يعمل مع البروتوكول <code>file://</code> - يحتاج <code>http://</code> أو <code>https://</code></p>
        </div>

        <div class="solution">
            <h3>💡 الحل الأمثل: خادم محلي بسيط</h3>
            <p>تشغيل خادم محلي بسيط لحل مشكلة البروتوكول</p>
        </div>

        <div class="step">
            <h4>🚀 الحل الأول: Python (الأسهل)</h4>
            <p>إذا كان لديك Python مثبت:</p>
            <ol>
                <li>افتح <strong>Command Prompt</strong> أو <strong>PowerShell</strong></li>
                <li>انتقل لمجلد التطبيق:</li>
                <div class="code-box">cd "C:\Users\<USER>\OneDrive\Desktop\اندرويد جديد\New folder"</div>
                <li>شغل الخادم:</li>
                <div class="code-box">python -m http.server 8000</div>
                <li>افتح في المتصفح:</li>
                <div class="code-box">http://localhost:8000/index.html</div>
            </ol>
            <button onclick="copyCommand('python')">📋 نسخ أوامر Python</button>
        </div>

        <div class="step">
            <h4>🌐 الحل الثاني: Node.js</h4>
            <p>إذا كان لديك Node.js مثبت:</p>
            <ol>
                <li>افتح <strong>Command Prompt</strong></li>
                <li>ثبت http-server:</li>
                <div class="code-box">npm install -g http-server</div>
                <li>انتقل لمجلد التطبيق:</li>
                <div class="code-box">cd "C:\Users\<USER>\OneDrive\Desktop\اندرويد جديد\New folder"</div>
                <li>شغل الخادم:</li>
                <div class="code-box">http-server -p 8000</div>
                <li>افتح في المتصفح:</li>
                <div class="code-box">http://localhost:8000/index.html</div>
            </ol>
            <button onclick="copyCommand('node')">📋 نسخ أوامر Node.js</button>
        </div>

        <div class="step">
            <h4>🔧 الحل الثالث: Live Server (VS Code)</h4>
            <p>إذا كنت تستخدم VS Code:</p>
            <ol>
                <li>ثبت إضافة <strong>Live Server</strong></li>
                <li>افتح مجلد التطبيق في VS Code</li>
                <li>انقر بالزر الأيمن على <code>index.html</code></li>
                <li>اختر <strong>Open with Live Server</strong></li>
            </ol>
        </div>

        <div class="step">
            <h4>⚡ الحل الرابع: خادم محلي جاهز</h4>
            <p>تحميل خادم محلي بسيط:</p>
            <ol>
                <li>حمل <strong>XAMPP</strong> أو <strong>WAMP</strong></li>
                <li>انسخ مجلد التطبيق إلى <code>htdocs</code></li>
                <li>شغل Apache</li>
                <li>افتح <code>http://localhost/New folder/index.html</code></li>
            </ol>
            <button onclick="openXAMPP()">🔗 تحميل XAMPP</button>
        </div>

        <div class="info-box">
            <h3>🎯 بعد تشغيل الخادم المحلي:</h3>
            <ul>
                <li>✅ Google Drive API سيعمل بشكل طبيعي</li>
                <li>✅ تسجيل الدخول سيعمل بدون مشاكل</li>
                <li>✅ المزامنة ستعمل بشكل كامل</li>
                <li>✅ جميع المزايا ستكون متاحة</li>
            </ul>
        </div>

        <div class="step">
            <h4>🧪 اختبار سريع</h4>
            <p>للتأكد من عمل الحل:</p>
            <button onclick="testLocalServer()" class="btn-primary">🧪 اختبار الخادم المحلي</button>
            <button onclick="openMainApp()" class="btn-warning">🏠 فتح التطبيق الرئيسي</button>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="goBack()" style="background: #6c757d;">
                ↩️ العودة لأداة الإصلاح
            </button>
        </div>
    </div>

    <script>
        function copyCommand(type) {
            let commands = '';
            
            if (type === 'python') {
                commands = `cd "C:\\Users\\<USER>\\OneDrive\\Desktop\\اندرويد جديد\\New folder"
python -m http.server 8000

ثم افتح: http://localhost:8000/index.html`;
            } else if (type === 'node') {
                commands = `npm install -g http-server
cd "C:\\Users\\<USER>\\OneDrive\\Desktop\\اندرويد جديد\\New folder"
http-server -p 8000

ثم افتح: http://localhost:8000/index.html`;
            }
            
            navigator.clipboard.writeText(commands).then(() => {
                alert('✅ تم نسخ الأوامر! الصقها في Command Prompt');
            }).catch(() => {
                prompt('انسخ هذه الأوامر:', commands);
            });
        }

        function openXAMPP() {
            window.open('https://www.apachefriends.org/download.html', '_blank');
        }

        function testLocalServer() {
            const testUrl = 'http://localhost:8000/index.html';
            
            // Try to open the local server
            const testWindow = window.open(testUrl, '_blank');
            
            if (testWindow) {
                alert('✅ تم فتح الخادم المحلي!\nإذا ظهرت الصفحة، فالحل يعمل بشكل صحيح.');
            } else {
                alert('❌ لم يتم العثور على خادم محلي.\nتأكد من تشغيل الخادم أولاً.');
            }
        }

        function openMainApp() {
            // Try local server first, then file
            const localUrl = 'http://localhost:8000/index.html';
            const fileUrl = 'index.html';
            
            try {
                window.open(localUrl, '_blank');
            } catch (e) {
                window.open(fileUrl, '_blank');
            }
        }

        function goBack() {
            window.location.href = 'google-drive-emergency-fix.html';
        }

        // Auto-check for local server
        window.addEventListener('load', function() {
            // Check if we're already on a local server
            if (window.location.protocol === 'http:' || window.location.protocol === 'https:') {
                document.body.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                
                const successMsg = document.createElement('div');
                successMsg.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                    z-index: 1000;
                `;
                successMsg.innerHTML = '✅ أنت تستخدم خادم محلي! Google Drive سيعمل الآن.';
                document.body.appendChild(successMsg);
                
                setTimeout(() => {
                    successMsg.remove();
                }, 5000);
            }
        });
    </script>
</body>
</html>
