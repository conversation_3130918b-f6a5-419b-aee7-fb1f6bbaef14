<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة النسور الماسية للتجارة - نظام إدارة مخزون بطاريات الدواجن (نسخة محلية)</title>
    
    <!-- Prevent Caching for Mobile Updates -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="cache-control" content="no-cache">
    <meta name="expires" content="0">
    <meta name="pragma" content="no-cache">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="النسور الماسية">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Force Refresh Meta -->
    <meta name="version" content="2024.12.12.001">
    <meta name="last-modified" content="2024-12-12T12:00:00Z">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦅</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦅</text></svg>">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Firebase SDK for Web - DISABLED -->
    <script src="firebase-disabled.js"></script>

    <!-- Styles with Cache Busting -->
    <link rel="stylesheet" href="style.css?v=2024.12.12.001">
    
    <style>
        /* رسالة النسخة المحلية */
        .local-notice {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        body {
            padding-top: 50px;
        }
        
        /* إخفاء شاشة التفعيل نهائياً */
        #licenseScreen {
            display: none !important;
        }
        
        /* إخفاء قسم Google Drive */
        .cloud-storage-section {
            display: none !important;
        }
        
        /* تنبيه التخزين المحلي */
        .local-storage-notice {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- رسالة النسخة المحلية -->
    <div class="local-notice">
        💻 النسخة المحلية - التخزين في المتصفح فقط | للمزامنة السحابية استخدم diamond-eagles-store.netlify.app
    </div>

    <!-- Login Check Script -->
    <script>
        // تعطيل Google Drive للنسخة المحلية
        window.DISABLE_GOOGLE_DRIVE = true;
        
        // Force show cloud storage on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💻 النسخة المحلية - تم تعطيل Google Drive');
            
            // Hide Google Drive sections
            const cloudSections = document.querySelectorAll('.cloud-storage-section, [id*="google"], [class*="google"]');
            cloudSections.forEach(section => {
                if (section) section.style.display = 'none';
            });
            
            // Show local storage notice
            setTimeout(() => {
                showLocalStorageNotice();
            }, 2000);
        });
        
        function showLocalStorageNotice() {
            const notice = document.createElement('div');
            notice.className = 'local-storage-notice';
            notice.innerHTML = `
                <h4><i class="fas fa-info-circle"></i> تنبيه: النسخة المحلية</h4>
                <p><strong>التخزين:</strong> جميع البيانات محفوظة في متصفحك فقط</p>
                <p><strong>المزامنة:</strong> للمزامنة السحابية، استخدم الرابط: <a href="https://diamond-eagles-store.netlify.app" target="_blank">diamond-eagles-store.netlify.app</a></p>
                <p><strong>النسخ الاحتياطية:</strong> ننصح بتصدير البيانات بانتظام</p>
            `;
            
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(notice, container.firstChild);
            }
        }
    </script>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="logo-text">
                        <h1>شركة النسور الماسية للتجارة</h1>
                        <p>نظام إدارة مخزون بطاريات الدواجن (نسخة محلية)</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="user-info">
                        <span class="user-name" id="currentUserName">مرحباً بك</span>
                        <button class="logout-btn" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل خروج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul class="nav-list">
                <li><a href="#" onclick="showSection('dashboard')" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>
                <li><a href="#" onclick="showSection('products')" class="nav-link" data-section="products">
                    <i class="fas fa-boxes"></i> إدارة المنتجات
                </a></li>
                <li><a href="#" onclick="showSection('customers')" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i> طلبات العملاء
                </a></li>
                <li><a href="#" onclick="showSection('settings')" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i> الإعدادات
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- Dashboard Section -->
    <section id="dashboard" class="section active">
        <div class="container">
            <h2>لوحة التحكم الرئيسية</h2>
            
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إدارة المنتجات</h3>
                        <p>نظام شامل لإدارة المخزون</p>
                        <div class="stat-numbers">
                            <div class="stat-number">
                                <span class="number" id="totalProducts">٠</span>
                                <span class="label">إجمالي المنتجات</span>
                            </div>
                            <div class="stat-number">
                                <span class="number" id="availableProducts">٠</span>
                                <span class="label">متاح</span>
                            </div>
                            <div class="stat-number">
                                <span class="number" id="reservedProducts">٠</span>
                                <span class="label">محجوز</span>
                            </div>
                        </div>
                        <div class="stat-actions">
                            <button onclick="showSection('products')" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة منتج جديد
                            </button>
                            <button onclick="showSection('products')" class="btn btn-secondary">
                                <i class="fas fa-search"></i> البحث والفلترة
                            </button>
                            <button onclick="exportProducts()" class="btn btn-success">
                                <i class="fas fa-download"></i> تصدير التقارير
                            </button>
                        </div>
                        <div class="capacity-info">
                            <span>السعة الإجمالية: <strong id="totalCapacity">٠</strong> طائر</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="totalCustomers">٠</span>
                            <span class="label">إجمالي العملاء</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-egg"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="breedingProducts">٠</span>
                            <span class="label">تربية</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="productionProducts">٠</span>
                            <span class="label">إنتاج</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-content">
                    <h3>مرحباً بك في نظام إدارة المخزون (النسخة المحلية)</h3>
                    <p>نظام متكامل لإدارة منتجات بطاريات الدواجن وطلبات العملاء بكل سهولة واحترافية</p>
                    
                    <div class="quick-actions">
                        <button onclick="showSection('products')" class="action-btn">
                            <i class="fas fa-plus-circle"></i>
                            <span>إضافة منتج جديد</span>
                        </button>
                        <button onclick="showSection('customers')" class="action-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة عميل جديد</span>
                        </button>
                        <button onclick="showSection('settings')" class="action-btn">
                            <i class="fas fa-cogs"></i>
                            <span>إعدادات النظام</span>
                        </button>
                    </div>
                </div>
                
                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <h4>تقارير تفصيلية</h4>
                        <p>تقارير شاملة عن المخزون والمبيعات</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <h4>واجهة متجاوبة</h4>
                        <p>يعمل على جميع الأجهزة والشاشات</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-save"></i>
                        <h4>حفظ محلي</h4>
                        <p>البيانات محفوظة في متصفحك</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-file-export"></i>
                        <h4>تصدير متعدد الصيغ</h4>
                        <p>تصدير البيانات بصيغ مختلفة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Other sections will be loaded dynamically -->
    <div id="sectionsContainer"></div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>جميع الحقوق محفوظة لمطور التطبيق <strong>كريم واصل</strong> / 01022225982 - ********** لصالح <strong>شركة النسور الماسية للتجارة</strong> 2025</p>
            <p><small>النسخة المحلية - للمزامنة السحابية: <a href="https://diamond-eagles-store.netlify.app" target="_blank">diamond-eagles-store.netlify.app</a></small></p>
        </div>
    </footer>

    <!-- Scripts with Cache Busting -->
    <script src="script-no-license.js?v=2024.12.12.001"></script>
    <script src="script.js?v=2024.12.12.001"></script>

    <!-- Disable Google Drive Scripts -->
    <script>
        // Override Google Drive functions to prevent errors
        window.connectToGoogleDrive = function() {
            console.log('💻 Google Drive معطل في النسخة المحلية');
            alert('Google Drive غير متاح في النسخة المحلية.\nللمزامنة السحابية، استخدم: diamond-eagles-store.netlify.app');
            return Promise.resolve(false);
        };
        
        window.uploadAllToGoogleDrive = function() {
            console.log('💻 رفع Google Drive معطل في النسخة المحلية');
            alert('رفع البيانات غير متاح في النسخة المحلية.\nاستخدم تصدير البيانات بدلاً من ذلك.');
            return Promise.resolve(false);
        };
        
        window.downloadAllFromGoogleDrive = function() {
            console.log('💻 تحميل Google Drive معطل في النسخة المحلية');
            alert('تحميل البيانات غير متاح في النسخة المحلية.\nاستخدم استيراد البيانات بدلاً من ذلك.');
            return Promise.resolve(false);
        };
        
        // Set Google Drive as disabled
        window.isGoogleDriveConnected = false;
        window.isGoogleDriveReady = false;
        window.googleDriveUser = null;
        
        console.log('💻 تم تحميل النسخة المحلية - Google Drive معطل');
    </script>
</body>
</html>
