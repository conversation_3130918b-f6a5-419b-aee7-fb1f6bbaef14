<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة النسور الماسية للتجارة - نظام إدارة مخزون بطاريات الدواجن</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* إخفاء شاشة التفعيل */
        #licenseScreen {
            display: none !important;
        }
        
        /* التأكد من ظهور التطبيق الرئيسي */
        .header, .section {
            display: block !important;
        }
        
        /* رسالة تنبيه للنسخة التجريبية */
        .demo-notice {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        body {
            padding-top: 50px;
        }
    </style>
</head>
<body>
    <!-- رسالة النسخة التجريبية -->
    <div class="demo-notice">
        🎯 النسخة التجريبية - بدون نظام التراخيص | يمكن الوصول من جميع الأجهزة
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="logo-text">
                        <h1>شركة النسور الماسية للتجارة</h1>
                        <p>نظام إدارة مخزون بطاريات الدواجن</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="user-info">
                        <span class="user-name" id="currentUserName">مرحباً بك</span>
                        <button class="logout-btn" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل خروج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul class="nav-list">
                <li><a href="#" onclick="showSection('dashboard')" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>
                <li><a href="#" onclick="showSection('products')" class="nav-link" data-section="products">
                    <i class="fas fa-boxes"></i> إدارة المنتجات
                </a></li>
                <li><a href="#" onclick="showSection('customers')" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i> طلبات العملاء
                </a></li>
                <li><a href="#" onclick="showSection('settings')" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i> الإعدادات
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- Dashboard Section -->
    <section id="dashboard" class="section active">
        <div class="container">
            <h2>لوحة التحكم الرئيسية</h2>
            
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إدارة المنتجات</h3>
                        <p>نظام شامل لإدارة المخزون</p>
                        <div class="stat-numbers">
                            <div class="stat-number">
                                <span class="number" id="totalProducts">٠</span>
                                <span class="label">إجمالي المنتجات</span>
                            </div>
                            <div class="stat-number">
                                <span class="number" id="availableProducts">٠</span>
                                <span class="label">متاح</span>
                            </div>
                            <div class="stat-number">
                                <span class="number" id="reservedProducts">٠</span>
                                <span class="label">محجوز</span>
                            </div>
                        </div>
                        <div class="stat-actions">
                            <button onclick="showSection('products')" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة منتج جديد
                            </button>
                            <button onclick="showSection('products')" class="btn btn-secondary">
                                <i class="fas fa-search"></i> البحث والفلترة
                            </button>
                            <button onclick="exportProducts()" class="btn btn-success">
                                <i class="fas fa-download"></i> تصدير التقارير
                            </button>
                        </div>
                        <div class="capacity-info">
                            <span>السعة الإجمالية: <strong id="totalCapacity">٠</strong> طائر</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="totalCustomers">٠</span>
                            <span class="label">إجمالي العملاء</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-egg"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="breedingProducts">٠</span>
                            <span class="label">تربية</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <span class="number" id="productionProducts">٠</span>
                            <span class="label">إنتاج</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-content">
                    <h3>مرحباً بك في نظام إدارة المخزون</h3>
                    <p>نظام متكامل لإدارة منتجات بطاريات الدواجن وطلبات العملاء بكل سهولة واحترافية</p>
                    
                    <div class="quick-actions">
                        <button onclick="showSection('products')" class="action-btn">
                            <i class="fas fa-plus-circle"></i>
                            <span>إضافة منتج جديد</span>
                        </button>
                        <button onclick="showSection('customers')" class="action-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة عميل جديد</span>
                        </button>
                        <button onclick="showSection('settings')" class="action-btn">
                            <i class="fas fa-cogs"></i>
                            <span>إعدادات النظام</span>
                        </button>
                    </div>
                </div>
                
                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <h4>تقارير تفصيلية</h4>
                        <p>تقارير شاملة عن المخزون والمبيعات</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <h4>واجهة متجاوبة</h4>
                        <p>يعمل على جميع الأجهزة والشاشات</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <h4>حفظ آمن للبيانات</h4>
                        <p>نظام حفظ تلقائي ونسخ احتياطية</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-file-export"></i>
                        <h4>تصدير متعدد الصيغ</h4>
                        <p>تصدير البيانات بصيغ مختلفة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="section">
        <div class="container">
            <div class="section-header">
                <h2>إدارة المنتجات</h2>
                <button onclick="showAddProductModal()" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>الحالة:</label>
                    <select id="statusFilter" onchange="filterProducts()">
                        <option value="">جميع الحالات</option>
                        <option value="متاح">متاح</option>
                        <option value="محجوز">محجوز</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>الفئة:</label>
                    <select id="categoryFilter" onchange="filterProducts()">
                        <option value="">جميع الفئات</option>
                        <option value="انتاج">انتاج</option>
                        <option value="تربية">تربية</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button onclick="clearFilters()" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                    <button onclick="exportProducts()" class="btn btn-success">
                        <i class="fas fa-download"></i> تصدير النتائج
                    </button>
                </div>
            </div>

            <!-- Export Options -->
            <div class="export-options">
                <button onclick="exportToWord()" class="btn btn-info">
                    <i class="fas fa-file-word"></i> تصدير Word
                </button>
                <button onclick="exportToPDF()" class="btn btn-danger">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button onclick="showSection('products')" class="btn btn-warning">
                    <i class="fas fa-boxes"></i> إدارة المنتجات
                </button>
                <button onclick="showCalculator()" class="btn btn-secondary">
                    <i class="fas fa-calculator"></i> آلة حاسبة
                </button>
                <button onclick="deleteAllProducts()" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف الكل
                </button>
            </div>

            <!-- Products Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الشركة المصنعة</th>
                            <th>بلد المنشأ</th>
                            <th>الفئة</th>
                            <th>الحالة</th>
                            <th>تاريخ الفك</th>
                            <th>الكمية المتاحة</th>
                            <th>عدد الأدوار</th>
                            <th>عدد الخطوط</th>
                            <th>عدد العشوش/خط</th>
                            <th>مقاس العش (سم)</th>
                            <th>إجمالي عدد العشوش</th>
                            <th>السعة الإجمالية</th>
                            <th>السعر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- Products will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Customers Section -->
    <section id="customers" class="section">
        <div class="container">
            <div class="section-header">
                <h2>إدارة طلبات العملاء</h2>
                <button onclick="showAddCustomerModal()" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> إضافة عميل جديد
                </button>
                <button onclick="deleteAllCustomers()" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف الكل
                </button>
            </div>

            <!-- Customer Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>نوع العميل:</label>
                    <select id="customerTypeFilter" onchange="filterCustomers()">
                        <option value="">جميع أنواع العملاء</option>
                        <option value="عميل عادي">عميل عادي</option>
                        <option value="عميل محتمل">عميل محتمل</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>نوع الطلب:</label>
                    <select id="orderTypeFilter" onchange="filterCustomers()">
                        <option value="">جميع أنواع الطلبات</option>
                        <option value="تربية">تربية</option>
                        <option value="انتاج">انتاج</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>الجنسية:</label>
                    <select id="customerNationalityFilter" onchange="filterCustomers()">
                        <option value="">جميع الجنسيات</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button onclick="clearCustomerFilters()" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                </div>
            </div>

            <!-- Export Options -->
            <div class="export-options">
                <button onclick="exportCustomersToWord()" class="btn btn-info">
                    <i class="fas fa-file-word"></i> تصدير Word
                </button>
                <button onclick="exportCustomersToExcel()" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button onclick="exportCustomersToPDF()" class="btn btn-danger">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
            </div>

            <!-- Customers Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الجوال</th>
                            <th>الجنسية</th>
                            <th>نوع العميل</th>
                            <th>نوع الطلب</th>
                            <th>تفاصيل الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <!-- Customers will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Settings Section -->
    <section id="settings" class="section">
        <div class="container">
            <h2>إعدادات النظام</h2>
            
            <!-- Settings content will be loaded here -->
            <div class="settings-content">
                <!-- Company Settings -->
                <div class="settings-card">
                    <div class="settings-card-header">
                        <h3><i class="fas fa-building"></i> بيانات الشركة</h3>
                    </div>
                    <div class="settings-card-body">
                        <!-- Company form will be here -->
                    </div>
                </div>

                <!-- Cloud Storage Settings -->
                <div class="settings-card">
                    <div class="settings-card-header">
                        <h3><i class="fas fa-cloud"></i> نظام التخزين السحابي</h3>
                    </div>
                    <div class="settings-card-body">
                        <div class="cloud-storage-info">
                            <p><strong>جميع البيانات محفوظة في السحابة تلقائياً</strong></p>
                            
                            <div class="cloud-status">
                                <div class="status-item">
                                    <span class="label">حالة الاتصال:</span>
                                    <span class="value" id="cloudConnectionStatus">غير متصل</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">المستخدم:</span>
                                    <span class="value" id="cloudUserInfo">غير متصل</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">آخر مزامنة:</span>
                                    <span class="value" id="lastSyncTime">لم يتم بعد</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">مجلد البيانات:</span>
                                    <span class="value">النسور الماسية</span>
                                </div>
                            </div>

                            <div class="cloud-actions">
                                <button type="button" class="btn btn-primary" onclick="connectToGoogleDrive()">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل دخول Google
                                </button>
                                <button type="button" class="btn btn-success" onclick="uploadAllToGoogleDrive()">
                                    <i class="fas fa-cloud-upload-alt"></i> رفع جميع البيانات
                                </button>
                                <button type="button" class="btn btn-info" onclick="downloadAllFromGoogleDrive()">
                                    <i class="fas fa-cloud-download-alt"></i> تحميل جميع البيانات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="checkGoogleDriveConnection()">
                                    <i class="fas fa-wifi"></i> فحص الاتصال
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>جميع الحقوق محفوظة لمطور التطبيق <strong>كريم واصل</strong> / 01022225982 - 0594347807 لصالح <strong>شركة النسور الماسية للتجارة</strong> 2025</p>
        </div>
    </footer>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Calculator Modal -->
    <div id="calculatorModal" class="modal" style="display: none;">
        <div class="modal-content calculator-modal">
            <div class="modal-header">
                <h3>الآلة الحاسبة</h3>
                <span class="close" onclick="hideCalculator()">&times;</span>
            </div>
            <div class="calculator">
                <div class="calculator-display">
                    <input type="text" id="calculatorDisplay" readonly>
                </div>
                <div class="calculator-buttons">
                    <button onclick="clearCalculator()">C</button>
                    <button onclick="clearEntry()">CE</button>
                    <button onclick="deleteLast()">⌫</button>
                    <button onclick="appendToCalculator('/')">/</button>
                    
                    <button onclick="appendToCalculator('7')">7</button>
                    <button onclick="appendToCalculator('8')">8</button>
                    <button onclick="appendToCalculator('9')">9</button>
                    <button onclick="appendToCalculator('*')">×</button>
                    
                    <button onclick="appendToCalculator('4')">4</button>
                    <button onclick="appendToCalculator('5')">5</button>
                    <button onclick="appendToCalculator('6')">6</button>
                    <button onclick="appendToCalculator('-')">-</button>
                    
                    <button onclick="appendToCalculator('1')">1</button>
                    <button onclick="appendToCalculator('2')">2</button>
                    <button onclick="appendToCalculator('3')">3</button>
                    <button onclick="appendToCalculator('+')">+</button>
                    
                    <button onclick="appendToCalculator('0')" class="zero">0</button>
                    <button onclick="appendToCalculator('.')">.</button>
                    <button onclick="calculateResult()" class="equals">=</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="google-drive-sync.js"></script>
    <script src="api-integration.js"></script>
    <script>
        // تعطيل نظام التراخيص
        window.LICENSE_DISABLED = true;
        
        // تجاوز فحص الترخيص
        function checkAppLicense() {
            console.log('🎯 تم تعطيل فحص الترخيص - النسخة التجريبية');
            return true;
        }
        
        // تجاوز شاشة التفعيل
        function showLicenseScreen() {
            console.log('🎯 تم تجاوز شاشة التفعيل - النسخة التجريبية');
            return;
        }
        
        // دخول مباشر للتطبيق
        function enterApp() {
            console.log('🎯 دخول مباشر للتطبيق - النسخة التجريبية');
            const licenseScreen = document.getElementById('licenseScreen');
            if (licenseScreen) {
                licenseScreen.style.display = 'none';
            }
            
            // إظهار التطبيق الرئيسي
            const header = document.querySelector('.header');
            const sections = document.querySelectorAll('.section');
            
            if (header) header.style.display = 'block';
            sections.forEach(section => {
                section.style.display = 'block';
            });
        }
        
        // تهيئة التطبيق بدون فحص الترخيص
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل النسخة التجريبية - بدون نظام التراخيص');
            
            // إخفاء شاشة التفعيل
            const licenseScreen = document.getElementById('licenseScreen');
            if (licenseScreen) {
                licenseScreen.style.display = 'none';
            }
            
            // دخول التطبيق مباشرة
            enterApp();
            
            // تحميل النظام الأساسي
            setTimeout(() => {
                if (typeof initializeApp === 'function') {
                    initializeApp();
                }
            }, 1000);
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
