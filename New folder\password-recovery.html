<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استرداد كلمة المرور - النسور الماسية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .recovery-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        
        .recovery-header {
            margin-bottom: 30px;
        }
        
        .recovery-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        .recovery-form {
            text-align: right;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="email"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            width: 100%;
            margin: 10px 0;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .recovery-steps {
            text-align: right;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .recovery-steps h4 {
            margin-top: 0;
            color: #667eea;
        }
        
        .recovery-steps ol {
            margin: 0;
            padding-right: 20px;
        }
        
        .recovery-steps li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="recovery-container">
        <div class="recovery-header">
            <div class="recovery-icon">
                <i class="fas fa-key"></i>
            </div>
            <h1>استرداد كلمة المرور</h1>
            <p>أدخل بريدك الإلكتروني لاستلام كلمة المرور</p>
        </div>

        <div class="recovery-form">
            <form id="recoveryForm" onsubmit="sendPasswordRecovery(event)">
                <div class="form-group">
                    <label for="recoveryEmail">البريد الإلكتروني:</label>
                    <input type="email" id="recoveryEmail" required placeholder="أدخل بريدك الإلكتروني">
                </div>
                
                <button type="submit" class="btn">
                    <i class="fas fa-paper-plane"></i> إرسال كلمة المرور
                </button>
            </form>
            
            <div class="loading" id="loadingSpinner">
                <div class="spinner"></div>
                <p>جاري الإرسال...</p>
            </div>
            
            <div id="statusMessage"></div>
            
            <div class="recovery-steps">
                <h4>📋 خطوات الاسترداد:</h4>
                <ol>
                    <li>أدخل بريدك الإلكتروني المسجل في النظام</li>
                    <li>اضغط "إرسال كلمة المرور"</li>
                    <li>تحقق من بريدك الإلكتروني (وصندوق الرسائل غير المرغوبة)</li>
                    <li>استخدم كلمة المرور المرسلة لتسجيل الدخول</li>
                </ol>
            </div>
            
            <button onclick="goToLogin()" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة لتسجيل الدخول
            </button>
        </div>
    </div>

    <!-- Email Service -->
    <script src="email-service.js"></script>

    <script>
        // Email service will be initialized automatically

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('statusMessage').style.display = 'none';
        }

        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
            document.getElementById('recoveryForm').style.display = show ? 'none' : 'block';
        }

        async function sendPasswordRecovery(event) {
            event.preventDefault();

            const email = document.getElementById('recoveryEmail').value.trim();

            if (!email) {
                showStatus('❌ يرجى إدخال البريد الإلكتروني', 'error');
                return;
            }

            // Validate email format
            if (!window.emailService.validateEmail(email)) {
                showStatus('❌ تنسيق البريد الإلكتروني غير صحيح', 'error');
                return;
            }

            showLoading(true);
            hideStatus();

            try {
                // Check if user exists in system
                const userData = await window.emailService.findUserByEmail(email);

                if (!userData) {
                    showLoading(false);
                    showStatus('❌ البريد الإلكتروني غير مسجل في النظام', 'error');
                    return;
                }

                // Send email using email service
                const result = await window.emailService.sendPasswordRecovery(email, userData);

                showLoading(false);

                if (result.success) {
                    showStatus(`✅ تم إرسال كلمة المرور إلى ${email} بنجاح!<br>تحقق من بريدك الإلكتروني (وصندوق الرسائل غير المرغوبة)`, 'success');

                    // Clear form
                    document.getElementById('recoveryEmail').value = '';
                } else {
                    // Show demo recovery as fallback
                    console.log('📧 فشل الإرسال، عرض البيانات الافتراضية');
                    await showDemoRecovery(email);
                }

            } catch (error) {
                console.error('خطأ في إرسال البريد:', error);
                showLoading(false);
                showStatus('❌ حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة لاحقاً.', 'error');
            }
        }

        // Simple fallback for demo purposes
        async function showDemoRecovery(email) {
            showStatus(`
                <div style="text-align: right;">
                    <h4>🔑 بيانات تسجيل الدخول الافتراضية:</h4>
                    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>كلمة المرور:</strong> 2030</p>
                    <br>
                    <p><small>💡 في النسخة الكاملة، سيتم إرسال كلمة المرور إلى بريدك الإلكتروني</small></p>
                </div>
            `, 'info');
        }

        function goToLogin() {
            window.location.href = 'login-system.html';
        }

        // Auto-focus on email input
        window.addEventListener('load', function() {
            document.getElementById('recoveryEmail').focus();
        });

        // Handle Enter key
        document.getElementById('recoveryEmail').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendPasswordRecovery(e);
            }
        });
    </script>
</body>
</html>
