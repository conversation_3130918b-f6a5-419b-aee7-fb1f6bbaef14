// Permissions Control System for Diamond Eagles Inventory
// نظام التحكم في الصلاحيات لتطبيق النسور الماسية

console.log('🔐 تحميل نظام التحكم في الصلاحيات...');

/**
 * Apply permissions-based restrictions to UI
 */
function applyPermissionsRestrictions() {
    const currentUser = userManager.getCurrentUser();

    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول - محاولة تسجيل دخول تلقائي');
        // Try to auto-login with default admin credentials
        autoLoginDefaultAdmin();
        return;
    }
    
    console.log('🔒 تطبيق قيود الصلاحيات للمستخدم:', currentUser.name);
    
    // Hide/show navigation items based on permissions
    applyNavigationRestrictions(currentUser);
    
    // Hide/show dashboard sections based on permissions
    applyDashboardRestrictions(currentUser);
    
    // Hide/show action buttons based on permissions
    applyActionButtonsRestrictions(currentUser);
    
    // Hide/show settings sections based on permissions
    applySettingsRestrictions(currentUser);
}

/**
 * Apply navigation restrictions
 */
function applyNavigationRestrictions(user) {
    const navigationItems = {
        'nav-dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'nav-products': PERMISSIONS.VIEW_PRODUCTS,
        'nav-customers': PERMISSIONS.VIEW_CUSTOMERS,
        'nav-settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    Object.entries(navigationItems).forEach(([navClass, permission]) => {
        const navItem = document.querySelector(`.${navClass}`);
        if (navItem) {
            const parentLi = navItem.closest('li');
            if (parentLi) {
                if (user.permissions.includes(permission)) {
                    parentLi.style.display = 'block';
                } else {
                    parentLi.style.display = 'none';
                }
            }
        }
    });
}

/**
 * Apply dashboard restrictions
 */
function applyDashboardRestrictions(user) {
    console.log('📊 تطبيق قيود لوحة التحكم للمستخدم:', user.name);

    // Hide dashboard sections based on permissions
    const dashboardSections = document.querySelectorAll('#dashboard .dashboard-card, #dashboard .card, #dashboard .stat-card');

    dashboardSections.forEach(section => {
        const sectionTitle = section.querySelector('h3, h4, .card-title')?.textContent || '';
        const sectionContent = section.textContent || '';

        let shouldHide = false;
        let reason = '';

        // Products section
        if (sectionTitle.includes('المنتجات') || sectionContent.includes('المنتجات') ||
            section.querySelector('.btn[onclick*="products"]') || section.querySelector('[href*="products"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
                shouldHide = true;
                reason = 'لا توجد صلاحية عرض المنتجات';
            }
        }

        // Customers section
        if (sectionTitle.includes('العملاء') || sectionContent.includes('العملاء') ||
            section.querySelector('.btn[onclick*="customers"]') || section.querySelector('[href*="customers"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
                shouldHide = true;
                reason = 'لا توجد صلاحية عرض العملاء';
            }
        }

        // Settings section
        if (sectionTitle.includes('الإعدادات') || sectionContent.includes('الإعدادات') ||
            section.querySelector('.btn[onclick*="settings"]') || section.querySelector('[href*="settings"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
                shouldHide = true;
                reason = 'لا توجد صلاحية عرض الإعدادات';
            }
        }

        // User management sections
        if (sectionTitle.includes('المستخدمين') || sectionContent.includes('إدارة المستخدمين')) {
            if (!user.permissions.includes(PERMISSIONS.MANAGE_USERS)) {
                shouldHide = true;
                reason = 'لا توجد صلاحية إدارة المستخدمين';
            }
        }

        // Apply visibility
        if (shouldHide) {
            section.style.display = 'none';
            console.log(`🚫 إخفاء قسم لوحة التحكم: "${sectionTitle}" - ${reason}`);
        } else {
            section.style.display = '';
            console.log(`✅ عرض قسم لوحة التحكم: "${sectionTitle}"`);
        }
    });
}

/**
 * Apply action buttons restrictions
 */
function applyActionButtonsRestrictions(user) {
    console.log('🔘 تطبيق قيود أزرار الإجراءات للمستخدم:', user.name);

    // Products action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_PRODUCTS)) {
        hideElements('.btn[onclick*="addProduct"], .btn[onclick*="showAddProductModal"], .btn[onclick*="addNewProduct"]');
        console.log('🚫 إخفاء أزرار إضافة المنتجات');
    }

    if (!user.permissions.includes(PERMISSIONS.EDIT_PRODUCTS)) {
        hideElements('.btn[onclick*="editProduct"], .btn[onclick*="showEditProductModal"], .btn[onclick*="updateProduct"]');
        console.log('🚫 إخفاء أزرار تعديل المنتجات');
    }

    if (!user.permissions.includes(PERMISSIONS.DELETE_PRODUCTS)) {
        hideElements('.btn[onclick*="deleteProduct"], .btn[onclick*="removeProduct"]');
        console.log('🚫 إخفاء أزرار حذف المنتجات');
    }

    // Customers action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_CUSTOMERS)) {
        hideElements('.btn[onclick*="addCustomer"], .btn[onclick*="showAddCustomerModal"], .btn[onclick*="addNewCustomer"]');
        console.log('🚫 إخفاء أزرار إضافة العملاء');
    }

    if (!user.permissions.includes(PERMISSIONS.EDIT_CUSTOMERS)) {
        hideElements('.btn[onclick*="editCustomer"], .btn[onclick*="showEditCustomerModal"], .btn[onclick*="updateCustomer"]');
        console.log('🚫 إخفاء أزرار تعديل العملاء');
    }

    if (!user.permissions.includes(PERMISSIONS.DELETE_CUSTOMERS)) {
        hideElements('.btn[onclick*="deleteCustomer"], .btn[onclick*="removeCustomer"]');
        console.log('🚫 إخفاء أزرار حذف العملاء');
    }

    // Settings buttons
    if (!user.permissions.includes(PERMISSIONS.EDIT_SETTINGS)) {
        hideElements('#settings .btn[type="submit"], #settings .btn[onclick*="save"], #settings .btn[onclick*="update"]');
        console.log('🚫 إخفاء أزرار حفظ الإعدادات');
    }

    // User management buttons
    if (!user.permissions.includes(PERMISSIONS.MANAGE_USERS)) {
        hideElements('.btn[onclick*="addUser"], .btn[onclick*="editUser"], .btn[onclick*="deleteUser"], .btn[onclick*="manageUsers"]');
        console.log('🚫 إخفاء أزرار إدارة المستخدمين');
    }

    // Admin-only buttons
    if (user.role !== USER_ROLES.ADMIN) {
        hideElements('.admin-only, .btn.admin-only, [data-admin-only="true"]');
        console.log('🚫 إخفاء الأزرار المخصصة للمدير فقط');
    }
}

/**
 * Apply settings restrictions
 */
function applySettingsRestrictions(user) {
    if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        const settingsSection = document.getElementById('settings');
        if (settingsSection) {
            settingsSection.style.display = 'none';
        }
        return;
    }
    
    // Hide user management section if no permission
    if (!user.permissions.includes(PERMISSIONS.MANAGE_USERS)) {
        // Find user management card by text content
        const settingsCards = document.querySelectorAll('.settings-card');
        settingsCards.forEach(card => {
            const heading = card.querySelector('h3');
            if (heading && heading.textContent.includes('إدارة المستخدمين')) {
                card.style.display = 'none';
                console.log('🚫 إخفاء قسم إدارة المستخدمين - لا توجد صلاحية');
            }
        });
    }
    
    // Hide sensitive settings for non-admin users
    if (user.role !== USER_ROLES.ADMIN) {
        hideElements('#settings .developer-actions, #settings .system-actions');
        console.log('🚫 إخفاء الإعدادات الحساسة - المستخدم ليس مدير');
    }

    // Apply detailed settings restrictions
    applyDetailedSettingsRestrictions(user);
}

/**
 * Apply detailed settings restrictions
 */
function applyDetailedSettingsRestrictions(user) {
    console.log('🔧 تطبيق قيود الإعدادات التفصيلية للمستخدم:', user.name);
    console.log('🎭 دور المستخدم:', user.role);
    console.log('🔐 صلاحيات المستخدم:', user.permissions);

    // Settings sections to control
    const settingsSections = [
        {
            selector: '.settings-card',
            titleText: 'إعدادات التطبيق',
            permission: PERMISSIONS.VIEW_SETTINGS,
            description: 'إعدادات عامة للتطبيق'
        },
        {
            selector: '.settings-card',
            titleText: 'إدارة المستخدمين',
            permission: PERMISSIONS.MANAGE_USERS,
            description: 'إدارة حسابات المستخدمين'
        },
        {
            selector: '.settings-card',
            titleText: 'إعدادات النظام',
            permission: PERMISSIONS.VIEW_SETTINGS,
            adminOnly: true,
            description: 'إعدادات النظام المتقدمة'
        },
        {
            selector: '.settings-card',
            titleText: 'صيانة النظام',
            permission: PERMISSIONS.VIEW_SETTINGS,
            adminOnly: true,
            description: 'أدوات صيانة وإصلاح النظام'
        }
    ];

    settingsSections.forEach(section => {
        const hasPermission = user.permissions.includes(section.permission);
        const isAdmin = user.role === USER_ROLES.ADMIN;
        const shouldShow = hasPermission && (!section.adminOnly || isAdmin);

        // Find the section by title text
        const settingsCards = document.querySelectorAll(section.selector);
        settingsCards.forEach(card => {
            const heading = card.querySelector('h3');
            if (heading && heading.textContent.includes(section.titleText)) {
                if (shouldShow) {
                    card.style.display = '';
                    console.log(`✅ عرض قسم: ${section.titleText}`);
                } else {
                    card.style.display = 'none';
                    console.log(`🚫 إخفاء قسم: ${section.titleText} - ${section.description}`);
                }
            }
        });
    });
}

/**
 * Hide elements by selector
 */
function hideElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = 'none';
    });
}

/**
 * Show elements by selector
 */
function showElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = '';
    });
}

/**
 * Check if current user has permission
 */
function hasPermission(permission) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return false;
    return currentUser.permissions.includes(permission);
}

/**
 * Redirect to appropriate section based on permissions
 */
function redirectToAllowedSection() {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return;
    
    // Find first allowed section
    if (currentUser.permissions.includes(PERMISSIONS.VIEW_DASHBOARD)) {
        showSection('dashboard');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
        showSection('products');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
        showSection('customers');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        showSection('settings');
    } else {
        // No permissions - show error or logout
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحيات للوصول لأي قسم', 'error');
        }
        setTimeout(() => {
            logoutUser();
        }, 2000);
    }
}

/**
 * Override showSection function to check permissions
 */
const originalShowSection = window.showSection;
window.showSection = function(sectionName) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول');
        return;
    }
    
    // Check permissions for each section
    const sectionPermissions = {
        'dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'products': PERMISSIONS.VIEW_PRODUCTS,
        'customers': PERMISSIONS.VIEW_CUSTOMERS,
        'settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    const requiredPermission = sectionPermissions[sectionName];
    if (requiredPermission && !currentUser.permissions.includes(requiredPermission)) {
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحية للوصول لهذا القسم', 'error');
        }
        return;
    }
    
    // Call original function if permission is granted
    if (originalShowSection) {
        originalShowSection(sectionName);
    }
};

/**
 * Auto-login with default admin credentials
 */
function autoLoginDefaultAdmin() {
    try {
        console.log('🔐 محاولة تسجيل دخول تلقائي للمدير الافتراضي...');

        // Default admin credentials
        const defaultEmail = '<EMAIL>';
        const defaultPassword = '2030';

        // Check if default admin exists
        const allUsers = userManager.getAllUsers();
        const defaultAdmin = allUsers.find(user => user.email === defaultEmail);

        if (!defaultAdmin) {
            console.log('👤 المدير الافتراضي غير موجود - إنشاء حساب جديد...');
            // Create default admin user
            const adminData = {
                name: 'المدير الرئيسي',
                email: defaultEmail,
                password: defaultPassword,
                role: USER_ROLES.ADMIN,
                permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN]
            };

            const createResult = userManager.addUser(adminData);
            if (!createResult.success) {
                console.error('❌ فشل في إنشاء المدير الافتراضي:', createResult.error);
                showLoginPrompt();
                return;
            }
            console.log('✅ تم إنشاء المدير الافتراضي بنجاح');
        }

        // Try to authenticate
        const result = userManager.authenticateUser(defaultEmail, defaultPassword);

        if (result.success) {
            console.log('✅ تم تسجيل الدخول التلقائي بنجاح');
            // Apply permissions after successful login
            setTimeout(() => {
                updateUserDisplay();
                applyPermissionsRestrictions();
                redirectToAllowedSection();
                showWelcomeMessage();
            }, 500);
        } else {
            console.log('❌ فشل في تسجيل الدخول التلقائي:', result.error);
            // Show login prompt or redirect to login
            showLoginPrompt();
        }
    } catch (error) {
        console.error('❌ خطأ في تسجيل الدخول التلقائي:', error);
        showLoginPrompt();
    }
}

/**
 * Show login prompt for user
 */
function showLoginPrompt() {
    // Create a simple login prompt
    const loginPrompt = document.createElement('div');
    loginPrompt.id = 'loginPrompt';
    loginPrompt.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;

    loginPrompt.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 10px; text-align: center; max-width: 400px;">
            <h2 style="color: #333; margin-bottom: 20px;">🔐 تسجيل الدخول مطلوب</h2>
            <p style="color: #666; margin-bottom: 20px;">يجب تسجيل الدخول للوصول للنظام</p>

            <div style="margin-bottom: 15px;">
                <input type="email" id="loginEmail" placeholder="البريد الإلكتروني"
                       value="<EMAIL>"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
                <input type="password" id="loginPassword" placeholder="كلمة المرور"
                       value="2030"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
            </div>

            <button onclick="performLogin()"
                    style="background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                تسجيل الدخول
            </button>

            <p style="font-size: 12px; color: #999; margin-top: 15px;">
                المدير الافتراضي: <EMAIL> / 2030
            </p>
        </div>
    `;

    document.body.appendChild(loginPrompt);
}

/**
 * Perform login from prompt
 */
function performLogin() {
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    if (!email || !password) {
        alert('يرجى إدخال البريد الإلكتروني وكلمة المرور');
        return;
    }

    const result = userManager.authenticateUser(email, password);

    if (result.success) {
        // Remove login prompt
        const loginPrompt = document.getElementById('loginPrompt');
        if (loginPrompt) {
            loginPrompt.remove();
        }

        console.log('✅ تم تسجيل الدخول بنجاح');

        // Apply permissions and update UI
        setTimeout(() => {
            updateUserDisplay();
            applyPermissionsRestrictions();
            redirectToAllowedSection();
            showWelcomeMessage();
        }, 500);

    } else {
        alert('خطأ في تسجيل الدخول: ' + result.error);
    }
}

/**
 * Initialize permissions control
 */
function initializePermissionsControl() {
    // Apply restrictions after a delay to ensure DOM is ready
    setTimeout(() => {
        applyPermissionsRestrictions();
        redirectToAllowedSection();
    }, 1500);
}

/**
 * Update permissions when user changes
 */
function updatePermissionsForUser() {
    applyPermissionsRestrictions();
}

/**
 * Check and ensure user is logged in
 */
function ensureUserLoggedIn() {
    const currentUser = userManager.getCurrentUser();

    if (!currentUser) {
        console.log('🔐 لا يوجد مستخدم مسجل دخول - بدء عملية التسجيل');
        autoLoginDefaultAdmin();
    } else {
        console.log('✅ المستخدم مسجل دخول:', currentUser.name);
        applyPermissionsRestrictions();
        redirectToAllowedSection();
    }
}

/**
 * Initialize user session and permissions
 */
function initializeUserSession() {
    // Wait for user management system to be ready
    if (typeof userManager === 'undefined') {
        console.log('⏳ انتظار تحميل نظام إدارة المستخدمين...');
        setTimeout(initializeUserSession, 1000);
        return;
    }

    console.log('🔐 تهيئة جلسة المستخدم والصلاحيات...');
    ensureUserLoggedIn();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeUserSession();
    }, 2000);
});

/**
 * Update user display in UI
 */
function updateUserDisplay() {
    const currentUser = userManager.getCurrentUser();

    // Update current user display in settings
    const currentUserDisplay = document.getElementById('currentUserDisplay');
    if (currentUserDisplay && currentUser) {
        currentUserDisplay.textContent = currentUser.email;
    }

    // Update last login display
    const lastLoginDisplay = document.getElementById('lastLoginDisplay');
    if (lastLoginDisplay && currentUser && currentUser.lastLogin) {
        const lastLogin = new Date(currentUser.lastLogin).toLocaleString('ar-SA');
        lastLoginDisplay.textContent = lastLogin;
    }

    // Update any other user-related UI elements
    const userNameElements = document.querySelectorAll('.current-user-name');
    userNameElements.forEach(element => {
        if (currentUser) {
            element.textContent = currentUser.name;
        }
    });
}

/**
 * Show welcome message for logged in user
 */
function showWelcomeMessage() {
    const currentUser = userManager.getCurrentUser();
    if (currentUser && typeof showToast === 'function') {
        showToast(`مرحباً ${currentUser.name}`, 'success');
    }
}

/**
 * Refresh permissions (call this after user login/logout)
 */
function refreshPermissions() {
    console.log('🔄 تحديث الصلاحيات...');

    // First, show all elements
    showAllElements();

    // Then apply restrictions
    setTimeout(() => {
        applyPermissionsRestrictions();
    }, 100);
}

/**
 * Show all elements (reset visibility)
 */
function showAllElements() {
    console.log('👁️ إظهار جميع العناصر...');

    // Show all dashboard cards
    const dashboardElements = document.querySelectorAll('#dashboard .dashboard-card, #dashboard .card, #dashboard .stat-card');
    dashboardElements.forEach(element => {
        element.style.display = '';
    });

    // Show all settings cards
    const settingsElements = document.querySelectorAll('#settings .settings-card');
    settingsElements.forEach(element => {
        element.style.display = '';
    });

    // Show all buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.style.display = '';
    });

    // Show all navigation items
    const navItems = document.querySelectorAll('.main-nav li');
    navItems.forEach(item => {
        item.style.display = '';
    });
}

/**
 * Initialize permissions system when page loads
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 تهيئة نظام الصلاحيات...');

    // Wait for user management system to load
    setTimeout(() => {
        if (typeof userManager !== 'undefined') {
            applyPermissionsRestrictions();
        } else {
            console.error('❌ نظام إدارة المستخدمين غير متاح');
        }
    }, 1000);

    // Listen for user changes
    window.addEventListener('userChanged', function() {
        console.log('👤 تغيير المستخدم - تحديث الصلاحيات');
        refreshPermissions();
    });
});

// Export functions for global use
window.applyPermissionsRestrictions = applyPermissionsRestrictions;
window.hasPermission = hasPermission;
window.updatePermissionsForUser = updatePermissionsForUser;
window.ensureUserLoggedIn = ensureUserLoggedIn;
window.autoLoginDefaultAdmin = autoLoginDefaultAdmin;
window.performLogin = performLogin;
window.refreshPermissions = refreshPermissions;
window.showAllElements = showAllElements;

console.log('✅ تم تحميل نظام التحكم في الصلاحيات بنجاح');
