// Arabic Inventory Management System - No License Version

console.log('🦅 شركة النسور الماسية للتجارة - نظام إدارة المخزون (نسخة بدون تراخيص)');

// Global Variables
let products = [];
let customers = [];
let currentProductId = null;
let currentCustomerId = null;
let currentProductImages = []; // Store uploaded images for current product

// Country Phone Codes Mapping
const countryPhoneCodes = {
    'السعودية': '+966',
    'الإمارات العربية المتحدة': '+971',
    'الكويت': '+965',
    'قطر': '+974',
    'البحرين': '+973',
    'عمان': '+968',
    'الأردن': '+962',
    'لبنان': '+961',
    'سوريا': '+963',
    'العراق': '+964',
    'فلسطين': '+970',
    'مصر': '+20',
    'ليبيا': '+218',
    'تونس': '+216',
    'الجزائر': '+213',
    'المغرب': '+212',
    'السودان': '+249',
    'اليمن': '+967',
    'الصومال': '+252',
    'جيبوتي': '+253',
    'جزر القمر': '+269',
    'موريتانيا': '+222'
};

// Utility Functions
const utils = {
    // Generate unique ID
    generateId: function() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // Format currency
    formatCurrency: function(amount) {
        if (isNaN(amount)) return '0 ريال';
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },

    // Format date
    formatDate: function(date) {
        if (!date) return '';
        return new Date(date).toLocaleDateString('ar-SA');
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        console.log(`📢 ${type.toUpperCase()}: ${message}`);
    },

    // Validate email
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // Validate phone
    validatePhone: function(phone) {
        const re = /^[\+]?[0-9\-\(\)\s]+$/;
        return re.test(phone) && phone.length >= 8;
    }
};

// Storage Functions
const storage = {
    // Test localStorage availability
    testLocalStorage: function() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    },

    // Save products
    saveProducts: function() {
        try {
            localStorage.setItem('products', JSON.stringify(products));
            console.log('💾 تم حفظ المنتجات:', products.length);
        } catch (error) {
            console.error('❌ خطأ في حفظ المنتجات:', error);
            utils.showNotification('خطأ في حفظ المنتجات', 'error');
        }
    },

    // Load products
    loadProducts: function() {
        try {
            const savedProducts = localStorage.getItem('products');
            if (savedProducts) {
                products = JSON.parse(savedProducts);
                console.log('📦 تم تحميل المنتجات:', products.length);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المنتجات:', error);
            products = [];
        }
    },

    // Save customers
    saveCustomers: function() {
        try {
            localStorage.setItem('customers', JSON.stringify(customers));
            console.log('💾 تم حفظ العملاء:', customers.length);
        } catch (error) {
            console.error('❌ خطأ في حفظ العملاء:', error);
            utils.showNotification('خطأ في حفظ العملاء', 'error');
        }
    },

    // Load customers
    loadCustomers: function() {
        try {
            const savedCustomers = localStorage.getItem('customers');
            if (savedCustomers) {
                customers = JSON.parse(savedCustomers);
                console.log('👥 تم تحميل العملاء:', customers.length);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل العملاء:', error);
            customers = [];
        }
    },

    // Initialize sample data
    initializeSampleData: function() {
        // Only add sample data if no data exists
        if (products.length === 0) {
            console.log('📝 إضافة بيانات تجريبية...');
            // Add sample products here if needed
        }
        
        if (customers.length === 0) {
            console.log('👥 إضافة عملاء تجريبيين...');
            // Add sample customers here if needed
        }
    }
};

// Enhanced initialization without license check
function initializeApp() {
    // Prevent multiple initializations of core app
    if (window.coreAppInitialized) {
        console.log('⚠️ النظام الأساسي تم تهيئته مسبقاً، تخطي التهيئة المكررة');
        return;
    }

    console.log('تهيئة النظام...');
    window.coreAppInitialized = true;

    try {
        // Test localStorage first
        if (!storage.testLocalStorage()) {
            utils.showNotification('تحذير: التخزين المحلي غير متاح. قد تفقد البيانات عند تحديث الصفحة.', 'warning');
        }

        // Load data without clearing existing data
        storage.loadProducts();
        storage.loadCustomers();
        storage.initializeSampleData();

        console.log('Products loaded:', products.length);
        console.log('Customers loaded:', customers.length);

        // Update dashboard
        updateDashboardStats();

        // Load initial data for dashboard
        loadSectionData('dashboard');

        // Load products and customers tables if they exist
        if (document.getElementById('productsTableBody')) {
            loadProductsTable();
        }
        if (document.getElementById('customersTableBody')) {
            loadCustomersTable();
        }

        console.log('تم تهيئة النظام بنجاح');
        utils.showNotification('تم تحميل النظام بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تهيئة النظام:', error);
        utils.showNotification('حدث خطأ في تهيئة النظام', 'error');
    }

    // Save data before page unload
    window.addEventListener('beforeunload', () => {
        storage.saveProducts();
        storage.saveCustomers();
    });

    // Auto-save data every 30 seconds
    setInterval(() => {
        storage.saveProducts();
        storage.saveCustomers();
    }, 30000);
}

// Bypass license functions
function checkAppLicense() {
    console.log('✅ تم تجاوز فحص الترخيص - النسخة بدون تراخيص');
    return true;
}

function showLicenseScreen() {
    console.log('✅ تم تجاوز شاشة التفعيل - النسخة بدون تراخيص');
    return;
}

function enterApp() {
    console.log('✅ دخول مباشر للتطبيق - النسخة بدون تراخيص');
    
    // Hide license screen if exists
    const licenseScreen = document.getElementById('licenseScreen');
    if (licenseScreen) {
        licenseScreen.style.display = 'none';
    }
    
    // Show main app
    const header = document.querySelector('.header');
    const sections = document.querySelectorAll('.section');
    
    if (header) header.style.display = 'block';
    sections.forEach(section => {
        section.style.display = 'block';
    });
    
    // Show dashboard by default
    showSection('dashboard');
    
    // Initialize app
    initializeApp();
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل تطبيق النسور الماسية (نسخة بدون تراخيص)...');
    
    // Hide license screen immediately
    const licenseScreen = document.getElementById('licenseScreen');
    if (licenseScreen) {
        licenseScreen.style.display = 'none';
    }
    
    // Enter app directly
    enterApp();
});

// Export functions to global scope
window.initializeApp = initializeApp;
window.checkAppLicense = checkAppLicense;
window.showLicenseScreen = showLicenseScreen;
window.enterApp = enterApp;
window.utils = utils;
window.storage = storage;

console.log('✅ تم تحميل نظام النسور الماسية للتجارة بنجاح (نسخة بدون تراخيص)');
