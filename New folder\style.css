/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #2c3e50;
    direction: rtl;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.15),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(102, 126, 234, 0.3);
    backdrop-filter: blur(10px);
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 18px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.logo-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    z-index: 1;
}

.logo-icon:hover::before {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.4);
}

.logo-icon:hover::after {
    transform: rotate(45deg) translate(50%, 50%);
}

.logo-icon:hover {
    transform: scale(1.08) rotate(3deg);
    box-shadow:
        0 20px 50px rgba(102, 126, 234, 0.3),
        0 10px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(102, 126, 234, 0.5);
}

.logo-icon i {
    font-size: 32px;
    color: white;
    z-index: 2;
    position: relative;
}

/* Custom SVG Logo Icon */
.custom-logo-icon {
    width: 55px;
    height: 55px;
    z-index: 3;
    position: relative;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-icon:hover .custom-logo-icon {
    transform: scale(1.15);
    filter:
        drop-shadow(0 0 15px rgba(255, 215, 0, 0.6))
        drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Logo Icon for uploaded images */
.logo-icon img {
    width: 55px;
    height: 55px;
    object-fit: contain;
    z-index: 3;
    position: relative;
    border-radius: 12px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-icon:hover img {
    transform: scale(1.15);
    filter:
        drop-shadow(0 0 15px rgba(255, 255, 255, 0.3))
        drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Enhanced animated effects for the logo */
@keyframes logoPulse {
    0% {
        transform: scale(1);
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.15),
            0 5px 15px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            inset 0 -1px 0 rgba(102, 126, 234, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 20px 45px rgba(102, 126, 234, 0.3),
            0 8px 20px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            inset 0 -1px 0 rgba(102, 126, 234, 0.15);
    }
    100% {
        transform: scale(1);
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.15),
            0 5px 15px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            inset 0 -1px 0 rgba(102, 126, 234, 0.1);
    }
}

@keyframes logoGlow {
    0% {
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.15),
            0 5px 15px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            inset 0 -1px 0 rgba(102, 126, 234, 0.1);
    }
    50% {
        box-shadow:
            0 20px 50px rgba(102, 126, 234, 0.3),
            0 0 25px rgba(102, 126, 234, 0.2),
            0 8px 20px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            inset 0 -1px 0 rgba(102, 126, 234, 0.15);
    }
    100% {
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.15),
            0 5px 15px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            inset 0 -1px 0 rgba(102, 126, 234, 0.1);
    }
}

@keyframes frameGlow {
    0% {
        background: rgba(102, 126, 234, 0.05);
        border-color: rgba(102, 126, 234, 0.2);
    }
    50% {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.3);
    }
    100% {
        background: rgba(102, 126, 234, 0.05);
        border-color: rgba(102, 126, 234, 0.2);
    }
}

.logo-icon {
    animation: logoGlow 4s ease-in-out infinite;
}

.logo-icon::before {
    animation: frameGlow 4s ease-in-out infinite;
}

.logo-icon:hover {
    animation: logoPulse 0.8s ease-in-out;
}

.logo-icon:hover::before {
    animation: none;
}

/* SVG Animation */
@keyframes diamondRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.custom-logo-icon:hover polygon {
    animation: diamondRotate 2s linear infinite;
    transform-origin: center;
}

/* Logo container enhancement */
.logo-icon {
    /* Add subtle inner shadow for depth */
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Logo frame decoration */
.logo-icon::before {
    /* Inner frame styling */
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo-text h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2px;
}

.logo-text p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Exit Button */
.exit-app-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow:
        0 4px 15px rgba(255, 107, 107, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Cairo', sans-serif;
    min-width: 100px;
    justify-content: center;
}

.exit-app-btn:hover {
    background: linear-gradient(135deg, #ff5252, #e53935);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(255, 107, 107, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.2);
}

.exit-app-btn:active {
    transform: translateY(0) scale(1);
    box-shadow:
        0 2px 8px rgba(255, 107, 107, 0.3),
        0 1px 4px rgba(0, 0, 0, 0.1);
}

.exit-app-btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.exit-app-btn:hover i {
    transform: translateX(-2px);
}

.exit-app-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.exit-app-btn:hover::before {
    left: 100%;
}

/* Navigation */
.main-nav ul {
    list-style: none;
    display: flex;
    gap: 10px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    text-decoration: none;
    color: #6c757d;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 1;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-link i {
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

/* Dashboard Navigation - Blue */
.nav-dashboard::before {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.nav-dashboard:hover,
.nav-dashboard.active {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.nav-dashboard:hover i,
.nav-dashboard.active i {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Products Navigation - Green */
.nav-products::before {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

.nav-products:hover,
.nav-products.active {
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.nav-products:hover i,
.nav-products.active i {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Customers Navigation - Orange */
.nav-customers::before {
    background: linear-gradient(135deg, #fd7e14, #e55a00) !important;
}

.nav-customers:hover,
.nav-customers.active {
    box-shadow: 0 4px 15px rgba(253, 126, 20, 0.4);
}

.nav-customers:hover i,
.nav-customers.active i {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Settings Navigation - Purple */
.nav-settings::before {
    background: linear-gradient(135deg, #6f42c1, #5a2d91) !important;
}

.nav-settings:hover,
.nav-settings.active {
    box-shadow: 0 4px 15px rgba(111, 66, 193, 0.4);
}

.nav-settings:hover i,
.nav-settings.active i {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Additional hover effects */
.nav-link:hover {
    transform: translateY(-3px) scale(1.02);
}

.nav-link.active {
    transform: translateY(-2px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Icon animations */
.nav-link:hover i {
    transform: scale(1.2) rotate(5deg);
}

.nav-link.active i {
    transform: scale(1.1);
}

/* Text glow effect for active links */
.nav-link.active span {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    font-weight: 600;
}

/* Main Content */
.main {
    padding: 30px 0 60px 0;
    min-height: calc(100vh - 200px);
    position: relative;
}

.section {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.section.active {
    display: block;
}

/* FORCE HIDE settings cards outside settings section */
.section:not(#settings) .settings-card,
.section:not(#settings) .settings-container,
#dashboard .settings-card,
#dashboard .settings-container,
#products .settings-card,
#products .settings-container,
#customers .settings-card,
#customers .settings-container,
#requests .settings-card,
#requests .settings-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* FORCE SHOW settings only in settings section */
#settings .settings-container {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
    left: auto !important;
}

#settings.section.active .settings-card {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
    left: auto !important;
}

/* Additional safety rules */
body:not(.settings-active) .settings-card,
body:not(.settings-active) .settings-container {
    display: none !important;
}

body.settings-active #settings .settings-card,
body.settings-active #settings .settings-container {
    display: block !important;
}

/* Force hidden class */
.force-hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}

/* Enhanced settings cleanup */
html.settings-cleaned #dashboard .settings-card,
html.settings-cleaned #dashboard .settings-container,
html.settings-cleaned #products .settings-card,
html.settings-cleaned #products .settings-container,
html.settings-cleaned #customers .settings-card,
html.settings-cleaned #customers .settings-container,
html.settings-cleaned #requests .settings-card,
html.settings-cleaned #requests .settings-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    position: absolute !important;
    left: -10000px !important;
    top: -10000px !important;
    z-index: -999 !important;
}

/* Ensure settings show only in settings section */
html.settings-cleaned #settings.section.active .settings-card {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
}

html.settings-cleaned #settings.section.active .settings-container {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Headers */
.section-header,
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2,
.dashboard-header h2 {
    font-size: 2.2rem;
    color: white;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.4);
    background: linear-gradient(135deg, #ffffff, #f0f9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.95rem;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.85rem;
}

/* Enhanced Dashboard Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
    padding: 20px 0;
}

.stat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
}

/* Clickable stat cards */
.stat-card.clickable {
    cursor: pointer;
    user-select: none;
}

.stat-card.clickable:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

.stat-card.clickable:active {
    transform: translateY(-3px);
    transition: all 0.1s ease;
}

.stat-card.clickable::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-card.clickable:hover::before {
    left: 100%;
}

.click-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
    z-index: 2;
}

.stat-card.clickable:hover .click-indicator {
    opacity: 1;
    transform: scale(1);
}

.click-indicator i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Hover tooltip for clickable cards */
.stat-card.clickable::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.stat-card.clickable:hover::after {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 800;
    color: #0f172a;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.stat-content p {
    color: #334155;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    text-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Enhanced text contrast for better readability */
.stat-card:hover .stat-content h3 {
    font-weight: 900;
    text-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.stat-card:hover .stat-content p {
    font-weight: 800;
    text-shadow: 0 1px 4px rgba(0,0,0,0.15);
}

/* Additional contrast enhancement for all stat cards */
.stat-card {
    background: rgba(255, 255, 255, 0.99);
}

.stat-card .stat-content h3 {
    letter-spacing: -0.5px;
}

.stat-card .stat-content p {
    letter-spacing: 0.2px;
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 30px;
}

.welcome-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.welcome-card h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.welcome-card p {
    color: #6c757d;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.quick-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Filters Section */
.filters-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    flex-wrap: wrap;
}

.export-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.search-and-filters {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
}

/* Products Actions */
.products-actions {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: flex-end;
}

.export-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Info Button */
.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-2px);
}

.search-container {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.1rem;
}

.search-box input {
    width: 100%;
    padding: 15px 45px 15px 45px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.search-clear {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-clear:hover {
    background: #f8f9fa;
    color: #dc3545;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: center;
}

.filters-grid select {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    background: white;
    transition: border-color 0.3s ease;
    font-size: 0.95rem;
}

.filters-grid select:focus {
    outline: none;
    border-color: #667eea;
}

/* Advanced Filters */
.advanced-filters {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
    animation: slideDown 0.3s ease-out;
}

.advanced-filters-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #667eea;
}

.advanced-filters-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.advanced-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #667eea;
}

.price-range,
.quantity-range,
.capacity-range,
.date-range {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-range input,
.quantity-range input,
.capacity-range input,
.date-range input {
    flex: 1;
}

.price-range span,
.quantity-range span,
.capacity-range span,
.date-range span {
    color: #6c757d;
    font-weight: 500;
    white-space: nowrap;
}

.advanced-filters-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button States */
.btn.active {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

/* Enhanced Search Box */
.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
    max-width: 500px;
}

.search-box input {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-box input:focus {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

/* Filter Group Enhancements */
.filter-group {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.filter-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Range Input Styling */
.price-range,
.quantity-range,
.capacity-range,
.date-range {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Responsive Enhancements for Filters */
@media (max-width: 768px) {
    .search-container {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
        max-width: none;
    }

    .advanced-filters-grid {
        grid-template-columns: 1fr;
    }

    .price-range,
    .quantity-range,
    .capacity-range,
    .date-range {
        flex-direction: column;
        gap: 5px;
    }

    .price-range span,
    .quantity-range span,
    .capacity-range span,
    .date-range span {
        text-align: center;
        font-size: 0.8rem;
    }

    .advanced-filters-actions {
        flex-direction: column;
    }
}

/* Enhanced Table Responsive */
@media (max-width: 1200px) {
    .data-table {
        min-width: 1000px;
    }
}

@media (max-width: 992px) {
    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 8px 5px;
    }
}

/* Loading State for Filters */
.filters-loading {
    opacity: 0.6;
    pointer-events: none;
}

.filters-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Table Styles */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    min-width: 1200px;
}

.data-table th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.data-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.data-table tbody tr:nth-child(even):hover {
    background-color: #e9ecef;
}

/* Status Badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-available {
    background: #d4edda;
    color: #155724;
}

.status-reserved {
    background: #fff3cd;
    color: #856404;
}

.customer-regular {
    background: #d1ecf1;
    color: #0c5460;
}

.customer-potential {
    background: #f8d7da;
    color: #721c24;
}

/* Request Type Badges */
.request-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.request-breeding {
    background: #e7f3ff;
    color: #0066cc;
    border: 1px solid #b3d9ff;
}

.request-production {
    background: #fff2e7;
    color: #cc6600;
    border: 1px solid #ffcc99;
}

/* Customer Details Styles */
.customer-details {
    padding: 20px 0;
}

.detail-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.detail-section h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.detail-item .label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-left: 10px;
}

.detail-item .value {
    color: #212529;
    flex: 1;
}

.address-text, .request-text {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    margin: 0;
}

/* Form Grid Enhancements */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-buttons .btn {
    padding: 6px 10px;
    font-size: 0.8rem;
}

/* Customer Actions Modal Styles */
.customer-info-card {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
}

.customer-avatar {
    font-size: 4rem;
    color: #667eea;
    margin-left: 20px;
}

.customer-basic-info h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.5rem;
}

.customer-basic-info p {
    margin: 5px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.customer-badges {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 25px;
    padding: 10px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 25px 20px;
    border: 2px solid;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-decoration: none;
    color: inherit;
    min-height: 120px;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.action-btn:active {
    transform: translateY(-2px) scale(0.98);
}

.action-btn i {
    font-size: 2.5rem;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.action-btn:hover i {
    transform: scale(1.1);
}

.action-btn span {
    font-weight: 600;
    text-align: center;
    font-size: 0.95rem;
    line-height: 1.3;
}

/* Customer Action Buttons - Enhanced Colors */
.view-btn {
    border-color: #17a2b8;
    color: #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
}

.view-btn:hover {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.edit-btn {
    border-color: #007bff;
    color: #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.edit-btn:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.whatsapp-btn {
    border-color: #25d366;
    color: #25d366;
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(37, 211, 102, 0.05));
    box-shadow: 0 2px 8px rgba(37, 211, 102, 0.2);
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

.print-btn {
    border-color: #6f42c1;
    color: #6f42c1;
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.1), rgba(111, 66, 193, 0.05));
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.2);
}

.print-btn:hover {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
}

.export-btn {
    border-color: #28a745;
    color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.export-btn:hover {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.delete-btn {
    border-color: #dc3545;
    color: #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.delete-btn:hover {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.quote-btn {
    border-color: #6f42c1;
    color: #6f42c1;
}

.quote-btn:hover {
    background: #6f42c1;
    color: white;
}

.delete-btn {
    border-color: #dc3545;
    color: #dc3545;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

/* Export Options Modal */
.export-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.export-option-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 15px;
    border: 2px solid transparent;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-option-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.export-option-btn i {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.word-btn {
    border-color: #2b579a;
    color: #2b579a;
}

.word-btn:hover {
    background: #2b579a;
    color: white;
}

.pdf-btn {
    border-color: #dc3545;
    color: #dc3545;
}

.pdf-btn:hover {
    background: #dc3545;
    color: white;
}

.excel-btn {
    border-color: #217346;
    color: #217346;
}

.excel-btn:hover {
    background: #217346;
    color: white;
}

/* Clickable Row Styles */
.clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.clickable-row:hover {
    background-color: #f8f9fa !important;
}

/* Request Details Cell */
.request-details-cell {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
    padding: 8px !important;
}

.request-details-cell:hover {
    color: #495057;
    background-color: #f8f9fa;
    cursor: help;
}

/* Modal Sizes */
.small-modal {
    max-width: 400px;
}

.medium-modal {
    max-width: 600px;
}

.large-modal {
    max-width: 800px;
}

.extra-large-modal {
    max-width: 1200px;
    width: 95%;
}

/* Product Form Styles */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.form-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    font-size: 1.1rem;
}

/* Dynamic List Styles */
.dynamic-list-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.dynamic-list-item .form-control {
    flex: 1;
}

.dynamic-list-item .btn {
    flex-shrink: 0;
}

/* Image Upload Styles */
.image-upload-container {
    margin-top: 15px;
}

.image-upload-area {
    border: 2px dashed #667eea;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.image-upload-area:hover {
    border-color: #5a6fd8;
    background: #e9ecef;
}

.image-upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.image-upload-area p {
    color: #6c757d;
    margin: 0;
    font-size: 1.1rem;
}

.image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-preview-item {
    position: relative;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.image-preview-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-preview-item img:hover {
    transform: scale(1.05);
}

.image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.image-info {
    padding: 10px;
    background: #f8f9fa;
}

.image-name {
    display: block;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-size {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Image Modal */
.image-modal {
    max-width: 90%;
    max-height: 90%;
}

.full-image {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

/* Product Info Card */
.product-info-card {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
}

.product-avatar {
    font-size: 4rem;
    color: #667eea;
    margin-left: 20px;
}

.product-basic-info h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.5rem;
}

.product-basic-info p {
    margin: 5px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.product-badges {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Product Images Grid */
.product-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.product-image-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.product-image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.product-image-item img:hover {
    transform: scale(1.05);
}

/* Modern Products Table Styles */
.product-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.product-row:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Manufacturer Cell */
.manufacturer-cell {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

/* Country Cell */
.country-cell {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Category Badge */
.category-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.category-انتاج {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.category-تربية {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Status Cell Enhanced */
.status-cell {
    text-align: center;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-available {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-reserved {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Date Cell */
.date-cell {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Quantity Cell */
.quantity-cell {
    text-align: center;
}

.quantity-value {
    background: #e7f3ff;
    color: #0066cc;
    padding: 6px 12px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Technical Specs Cells */
.floors-cell, .lines-cell, .nests-cell {
    text-align: center;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

/* Nest Size Cell */
.nest-size-cell {
    text-align: center;
}

.nest-dimensions {
    background: #f8f9fa;
    color: #495057;
    padding: 6px 10px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.85rem;
    border: 1px solid #dee2e6;
}

/* Total Nests Cell */
.total-nests-cell {
    text-align: center;
}

.total-nests-value {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #dc2626;
    padding: 8px 12px;
    border-radius: 15px;
    font-weight: 700;
    font-size: 1rem;
    display: inline-block;
    border: 1px solid #fecaca;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}

.total-nests-unit {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 2px;
    font-weight: 500;
}

/* Capacity Cell */
.capacity-cell {
    text-align: center;
}

.capacity-value {
    background: linear-gradient(135deg, #fff2e7, #ffe8d1);
    color: #cc6600;
    padding: 8px 12px;
    border-radius: 15px;
    font-weight: 700;
    font-size: 1rem;
    display: block;
    margin-bottom: 2px;
}

.capacity-unit {
    color: #6c757d;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Barn Size Cell */
.barn-size-cell {
    text-align: center;
}

.barn-dimensions {
    background: #e9ecef;
    color: #495057;
    padding: 6px 10px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.85rem;
    border: 1px solid #ced4da;
    display: inline-block;
}

/* Price Cell */
.price-cell {
    text-align: center;
}

.price-value {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.1rem;
    border: 2px solid #b8dabc;
    display: inline-block;
}

/* Table Header Enhancements */
.data-table thead th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 15px 10px;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .data-table {
        font-size: 0.85rem;
    }

    .nest-dimensions, .barn-dimensions {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .capacity-value, .price-value {
        font-size: 0.9rem;
        padding: 6px 10px;
    }
}

@media (max-width: 768px) {
    .data-table {
        font-size: 0.8rem;
    }

    .status-badge, .category-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .quantity-value {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
}

/* Products Management Modal Styles */
.management-search {
    margin-bottom: 25px;
}

.products-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    max-height: 60vh;
    overflow-y: auto;
    padding: 10px;
}

.product-management-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-management-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.product-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.product-card-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.product-card-body {
    margin-bottom: 15px;
}

.product-card-body p {
    margin: 8px 0;
    color: #6c757d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.product-card-body i {
    width: 16px;
    color: #667eea;
}

.product-card-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.product-card-actions .btn {
    flex: 1;
    min-width: 60px;
}

/* Responsive for management grid */
@media (max-width: 768px) {
    .products-management-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .product-management-card {
        padding: 15px;
    }

    .product-card-actions {
        flex-direction: column;
        gap: 5px;
    }

    .product-card-actions .btn {
        flex: none;
    }
}

/* Settings Section Styles */
.settings-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.settings-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.settings-card-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.settings-card-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-card-header i {
    font-size: 1.4rem;
}

.settings-card-body {
    padding: 30px 25px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

/* Mobile Form Adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 14px 15px;
        font-size: 16px; /* Prevents zoom on iOS */
        border-radius: 6px;
    }

    .form-group textarea {
        min-height: 120px;
    }

    .form-section {
        padding: 15px;
        margin-bottom: 20px;
    }

    .form-section h3 {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 16px 15px;
        font-size: 16px;
    }

    .form-group label {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .dynamic-list-item {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .dynamic-list-item .btn {
        align-self: center;
        width: auto;
        min-width: 120px;
    }
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #e9ecef;
    border-radius: 4px;
    margin-left: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Upload Area Styles */
.upload-area {
    border: 3px dashed #667eea;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    margin-bottom: 20px;
}

.upload-area:hover {
    border-color: #5a6fd8;
    background: #e9ecef;
    transform: translateY(-2px);
}

.upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-area p {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin: 10px 0;
}

.upload-area small {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Documents List */
.documents-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.no-documents {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-documents p {
    font-size: 1.1rem;
    margin: 10px 0;
}

.no-documents small {
    font-size: 0.9rem;
    color: #adb5bd;
}

.error-message {
    text-align: center;
    color: #dc3545;
    font-weight: 600;
    padding: 20px;
}

.document-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: white;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.document-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.document-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.document-icon {
    font-size: 1.5rem;
    color: #667eea;
}

.document-details h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.document-details p {
    margin: 5px 0;
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    gap: 15px;
}

.file-size {
    font-weight: 600;
    color: #495057;
}

.file-date {
    color: #6c757d;
}

.file-type {
    color: #667eea;
    font-weight: 500;
    font-size: 0.75rem;
    background: #e7f3ff;
    padding: 2px 6px;
    border-radius: 10px;
    margin-top: 5px;
    display: inline-block;
}

.document-actions {
    display: flex;
    gap: 8px;
}

/* Document Viewer Styles */
.document-viewer {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.document-info {
    background: #e9ecef;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: right;
}

.document-info p {
    margin: 8px 0;
    color: #495057;
    font-size: 0.9rem;
}

.document-info strong {
    color: #2c3e50;
    font-weight: 600;
}

/* Backup Actions */
.backup-actions {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

/* Logo Section */
.logo-section {
    text-align: center;
}

.current-logo {
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    border: 2px dashed #e9ecef;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    overflow: hidden;
}

.logo-placeholder {
    text-align: center;
    color: #6c757d;
}

.logo-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
}

.current-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.logo-upload {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 15px;
}

.upload-note {
    color: #6c757d;
    font-style: italic;
}

/* Contact Numbers Section */
.contact-numbers-section {
    margin: 30px 0;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.contact-numbers-section h4 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-item {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.contact-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.contact-item .form-group {
    flex: 1;
    margin-bottom: 0;
}

.contact-item .contact-type {
    min-width: 150px;
}

.contact-item .contact-number {
    min-width: 200px;
}

.contact-item .contact-description {
    min-width: 180px;
}

.contact-item .remove-contact {
    flex-shrink: 0;
}

.contact-item input,
.contact-item select {
    margin-bottom: 0;
}

.contact-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 8px;
}

.contact-type-phone {
    background: #e7f3ff;
    color: #0066cc;
}

.contact-type-whatsapp {
    background: #e8f5e8;
    color: #25d366;
}

.contact-type-fax {
    background: #fff3cd;
    color: #856404;
}

.contact-type-landline {
    background: #f8d7da;
    color: #721c24;
}

.contact-type-emergency {
    background: #ffe6e6;
    color: #dc3545;
}

/* Date Preview */
.date-preview {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.date-format-preview {
    text-align: center;
}

.calendar-type {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
    color: #667eea;
    font-size: 1.1rem;
}

.calendar-type i {
    font-size: 1.2rem;
}

.date-display {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 15px 0;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
}

.date-note {
    margin-top: 10px;
    color: #6c757d;
    font-style: italic;
}

.date-note small {
    font-size: 0.85rem;
}

/* System Actions */
.system-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .backup-actions,
    .system-actions {
        flex-direction: column;
    }

    .logo-upload {
        flex-direction: column;
        align-items: center;
    }

    .settings-card-body {
        padding: 20px 15px;
    }
}

/* Product Specifications Modal Styles */
.product-specifications {
    max-height: 70vh;
    overflow-y: auto;
    padding: 10px;
}

.spec-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.spec-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.spec-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.spec-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.spec-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.spec-label {
    font-weight: 600;
    color: #495057;
    min-width: 140px;
    margin-left: 15px;
}

.spec-value {
    color: #212529;
    flex: 1;
    font-weight: 500;
}

.spec-value.highlight {
    background: linear-gradient(135deg, #fff2e7, #ffe8d1);
    color: #cc6600;
    padding: 8px 12px;
    border-radius: 15px;
    font-weight: 700;
    text-align: center;
}

.price-display {
    text-align: center;
    margin: 20px 0;
}

.price-amount {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    padding: 20px 30px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 2rem;
    border: 3px solid #b8dabc;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(21, 87, 36, 0.2);
}

.specifications-list, .warranty-list, .financial-list,
.included-list, .excluded-list, .payment-list, .validity-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.specifications-list li, .warranty-list li, .financial-list li,
.included-list li, .excluded-list li, .payment-list li, .validity-list li {
    background: white;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.specifications-list li:hover, .warranty-list li:hover, .financial-list li:hover,
.included-list li:hover, .excluded-list li:hover, .payment-list li:hover, .validity-list li:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.included-list li {
    border-left-color: #28a745;
}

.excluded-list li {
    border-left-color: #dc3545;
}

.warranty-list li {
    border-left-color: #17a2b8;
}

.financial-list li {
    border-left-color: #ffc107;
}

.payment-list li {
    border-left-color: #6f42c1;
}

.validity-list li {
    border-left-color: #fd7e14;
}

.product-images-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.gallery-image {
    border: 1px solid #dee2e6;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-image:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.gallery-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-image img:hover {
    transform: scale(1.05);
}

/* Export Options Enhancements */
.product-export-info {
    text-align: center;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.product-export-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.product-export-info p {
    margin: 5px 0;
    color: #6c757d;
}

.price-highlight {
    color: #155724 !important;
    font-weight: 700;
    font-size: 1.2rem;
}

.export-option-btn small {
    display: block;
    margin-top: 5px;
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Responsive Design for Specifications */
@media (max-width: 768px) {
    .spec-grid {
        grid-template-columns: 1fr;
    }

    .spec-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .spec-label {
        min-width: auto;
        margin-left: 0;
        margin-bottom: 5px;
    }

    .price-amount {
        font-size: 1.5rem;
        padding: 15px 20px;
    }

    .product-images-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Quote Creation Styles */
.quote-product-info {
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 2px solid #667eea;
}

.quote-product-info h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selected-product-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.product-summary h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.product-summary p {
    margin: 5px 0;
    color: #6c757d;
}

.product-price {
    color: #155724 !important;
    font-weight: 700;
    font-size: 1.2rem;
}

.product-specs-summary {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.spec-badge {
    background: #e7f3ff;
    color: #0066cc;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid #b3d9ff;
}

.customer-selection-section {
    margin-top: 30px;
}

.customer-selection-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.customer-search-box {
    position: relative;
    margin-bottom: 25px;
}

.customer-search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.customer-search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.customer-search-box input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    max-height: 50vh;
    overflow-y: auto;
    padding: 10px;
}

.customer-quote-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.customer-quote-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.customer-quote-card:active {
    transform: translateY(-1px);
}

.customer-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f1f3f4;
}

.customer-card-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.customer-card-body p {
    margin: 8px 0;
    color: #6c757d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-card-body i {
    width: 16px;
    color: #667eea;
}

.customer-card-footer {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #f1f3f4;
    text-align: center;
}

.customer-card-footer small {
    color: #adb5bd;
    font-size: 0.8rem;
}

/* Selected Customer Effect */
.customer-quote-card.selected {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.customer-quote-card.selected::after {
    content: "✓";
    position: absolute;
    top: 10px;
    left: 10px;
    background: #28a745;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

/* Responsive Design for Quote Modal */
@media (max-width: 768px) {
    .customers-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .customer-quote-card {
        padding: 15px;
    }

    .product-specs-summary {
        flex-direction: column;
        gap: 8px;
    }

    .spec-badge {
        text-align: center;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }

    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .action-btn {
        padding: 15px 10px;
    }

    .action-btn i {
        font-size: 1.5rem;
    }

    .customer-info-card {
        flex-direction: column;
        text-align: center;
    }

    .customer-avatar {
        margin: 0 0 15px 0;
    }
}









.phone-number {
    color: #ffffff;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0 2px;
    font-family: 'Arial', sans-serif;
    direction: ltr;
    display: inline-block;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }

    .nav-link {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .nav-link span {
        display: none;
    }

    .nav-link i {
        font-size: 1.3rem;
    }

    .nav-link:hover {
        transform: translateY(-2px) scale(1.05);
    }

    /* Fixed Footer Responsive */
    .copyright-text {
        font-size: 0.75rem;
        padding: 0 10px;
        line-height: 1.3;
    }

    .phone-numbers {
        display: block;
        margin-top: 2px;
        font-size: 0.8rem;
    }

    .section-header,
    .dashboard-header {
        flex-direction: column;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .filters-header {
        flex-direction: column;
        gap: 15px;
    }

    .search-and-filters {
        width: 100%;
    }

    .export-actions {
        justify-content: center;
        width: 100%;
    }

    .products-actions {
        justify-content: center;
    }

    .export-buttons {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 8px 5px;
    }
}

/* Loading and Empty States */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
    color: #dee2e6;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* Form Styles */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Actions Grid */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px 0;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    color: #2c3e50;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

.action-btn:hover {
    border-color: #667eea;
    background: #f8f9fa;
    transform: translateY(-2px);
}

.action-btn.danger {
    border-color: #dc3545;
    color: #dc3545;
}

.action-btn.danger:hover {
    background: #dc3545;
    color: white;
}

.action-btn i {
    font-size: 1.2rem;
}

/* WhatsApp Button */
.whatsapp-btn {
    background: #25D366 !important;
    color: white !important;
    border-color: #25D366 !important;
}

.whatsapp-btn:hover {
    background: #128C7E !important;
    border-color: #128C7E !important;
    transform: translateY(-2px);
}

/* Export Buttons */
.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
}

/* Customer Details Styles */
.customer-details {
    padding: 20px 0;
}

.detail-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.detail-item span {
    color: #2c3e50;
    font-size: 1rem;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.detail-section p {
    margin: 0;
    padding: 12px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    color: #2c3e50;
    line-height: 1.5;
}

/* Responsive Design for Details */
@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }

    .detail-item {
        margin-bottom: 10px;
    }

    /* Footer Responsive */
    .footer-content {
        gap: 20px;
    }

    .company-info h3 {
        font-size: 1.3rem;
    }

    .company-info p {
        font-size: 0.9rem;
    }

    .developer-info {
        padding: 15px;
    }

    .developer-title {
        font-size: 1.1rem;
    }

    .developer-name {
        font-size: 1.2rem;
    }

    .developer-contacts {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .contact-item {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .phone-number {
        font-size: 0.95rem;
    }

    .copyright-info p {
        font-size: 0.9rem;
        padding: 0 10px;
        text-align: center;
    }

    .copyright-info strong {
        display: block;
        margin-top: 5px;
        font-size: 1rem;
    }
}

/* Image Upload Styles */
.image-upload-section {
    margin-top: 20px;
}

.upload-area {
    border: 2px dashed #667eea;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area:hover {
    border-color: #5a67d8;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-area p {
    font-size: 1.1rem;
    color: #2c3e50;
    margin: 10px 0;
    font-weight: 600;
}

.upload-area small {
    color: #6c757d;
    font-size: 0.9rem;
}

.images-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.image-preview-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.image-preview-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.image-preview-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview-item img:hover {
    transform: scale(1.05);
}

.image-info {
    padding: 10px;
    background: #f8f9fa;
}

.image-name {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-size {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
}

.image-actions {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.image-actions .btn {
    flex: 1;
    margin: 0 2px;
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Image Modal Styles */
.image-modal {
    max-width: 90vw;
    max-height: 90vh;
}

.full-image-container {
    text-align: center;
}

.full-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.image-details {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: right;
}

.image-details p {
    margin: 8px 0;
    color: #2c3e50;
}

.image-details strong {
    color: #667eea;
}

/* Responsive Image Styles */
@media (max-width: 768px) {
    .images-preview {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-area i {
        font-size: 2rem;
    }

    .upload-area p {
        font-size: 1rem;
    }

    .image-preview-item img {
        height: 120px;
    }

    .image-modal {
        max-width: 95vw;
    }

    .full-image {
        max-height: 60vh;
    }
}

/* Large Modal Styles */
.large-modal {
    max-width: 90vw;
    max-height: 90vh;
    width: 1000px;
}

.large-modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Product Images Gallery */
.product-images-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.gallery-image {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-image:hover {
    transform: scale(1.05);
}

.gallery-image img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    cursor: pointer;
}

/* Detail List Styles */
.detail-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.detail-list li {
    padding: 8px 15px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 6px;
    border-right: 3px solid #667eea;
}

.detail-list li:hover {
    background: #e9ecef;
}

/* Responsive Large Modal */
@media (max-width: 768px) {
    .large-modal {
        max-width: 95vw;
        width: auto;
    }

    .large-modal .modal-body {
        max-height: 60vh;
    }

    .product-images-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
    }

    .gallery-image img {
        height: 80px;
    }
}

/* Custom Manufacturer Input */
#customManufacturer,
#editCustomManufacturer {
    border: 2px solid #667eea;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

#customManufacturer:focus,
#editCustomManufacturer:focus {
    border-color: #5a67d8;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

#customManufacturer::placeholder,
#editCustomManufacturer::placeholder {
    color: #667eea;
    font-style: italic;
}

/* Product and Customer Info */
.product-info,
.customer-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.product-info h4,
.customer-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.product-info p,
.customer-info p {
    margin: 0;
    color: #6c757d;
}

/* Enhanced Table Styles */
.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Enhanced Status Badges */
.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Buttons */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced Modal Animation */
.modal {
    animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Dynamic Items Styles */
.dynamic-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.dynamic-item input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    min-height: 50px;
    line-height: 1.5;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.dynamic-item input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* Enhanced styles for specifications and detailed inputs */
.form-section .dynamic-item {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-section .dynamic-item:hover {
    background: #f1f3f4;
    border-color: #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.form-section .dynamic-item input {
    min-height: 55px;
    padding: 18px 22px;
    font-size: 1.15rem;
    line-height: 1.6;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    background: white;
    color: #2c3e50;
    font-weight: 500;
}

.form-section .dynamic-item input:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.form-section .dynamic-item input::placeholder {
    color: #6c757d;
    font-size: 1.05rem;
    font-weight: 400;
}

/* Special styling for specifications container */
#editSpecificationsContainer .dynamic-item input,
#editWarrantyContainer .dynamic-item input,
#editFinancialOfferContainer .dynamic-item input,
#editIncludedItemsContainer .dynamic-item input,
#editExcludedItemsContainer .dynamic-item input,
#editPaymentMethodContainer .dynamic-item input,
#editOfferValidityContainer .dynamic-item input {
    min-height: 60px;
    padding: 20px 25px;
    font-size: 1.2rem;
    line-height: 1.7;
    border-radius: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.btn-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    width: 45px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.btn-remove:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-remove i {
    font-size: 1.1rem;
}

/* Enhanced button styling for form sections */
.form-section .dynamic-item .btn {
    min-height: 55px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.form-section .dynamic-item .btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    color: white;
    width: 50px;
    height: 60px;
    box-shadow: 0 3px 6px rgba(220, 53, 69, 0.25);
}

.form-section .dynamic-item .btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(220, 53, 69, 0.35);
}

/* Enhanced container styling for better visual hierarchy */
.form-section h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #667eea;
    font-size: 1.2rem;
}

/* Add button styling */
.form-section .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 1.05rem;
    font-weight: 600;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(40, 167, 69, 0.25);
}

.form-section .btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(40, 167, 69, 0.35);
}

/* Container spacing improvements */
.form-section > div[id$="Container"] {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.form-section > div[id$="Container"]:empty::before {
    content: "لا توجد عناصر مضافة بعد";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 20px;
    background: #f1f3f4;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

/* Developer Tools Styling */
.developer-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.developer-actions .btn {
    justify-content: flex-start;
    padding: 12px 20px;
    font-size: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.developer-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.developer-actions .btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.developer-actions .btn-info:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    border-color: #138496;
}

.developer-actions .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
}

.developer-actions .btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    border-color: #20c997;
}

.developer-actions .btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    border-color: #ffc107;
    color: #212529;
}

.developer-actions .btn-warning:hover {
    background: linear-gradient(135deg, #fd7e14, #dc3545);
    border-color: #fd7e14;
    color: white;
}

.developer-note {
    background: #e3f2fd;
    color: #1976d2;
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.developer-note i {
    margin-top: 2px;
    color: #2196f3;
}

/* Filtered Export Options Modal Styling */
.export-summary {
    margin-bottom: 30px;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 20px;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    padding: 25px;
    border-radius: 15px;
    border: 2px solid #2196f3;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.1);
}

.summary-icon {
    background: #2196f3;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.summary-content h3 {
    color: #1976d2;
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: bold;
}

.filter-description {
    color: #1565c0;
    margin: 5px 0;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 8px;
    display: inline-block;
}

.products-count {
    color: #0d47a1;
    margin: 10px 0 0 0;
    font-size: 16px;
}

.export-options-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
}

.export-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.export-option-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.export-option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all 0.3s ease;
}

.export-option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.pdf-option {
    border-color: #dc3545;
}

.pdf-option::before {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.pdf-option:hover {
    border-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5, #ffe6e6);
}

.word-option {
    border-color: #2b579a;
}

.word-option::before {
    background: linear-gradient(90deg, #2b579a, #1e3d72);
}

.word-option:hover {
    border-color: #2b579a;
    background: linear-gradient(135deg, #f0f4ff, #e6efff);
}

.excel-option {
    border-color: #217346;
}

.excel-option::before {
    background: linear-gradient(90deg, #217346, #1a5d37);
}

.excel-option:hover {
    border-color: #217346;
    background: linear-gradient(135deg, #f0fff4, #e6ffe6);
}

.option-icon {
    font-size: 48px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.pdf-option .option-icon {
    color: #dc3545;
}

.word-option .option-icon {
    color: #2b579a;
}

.excel-option .option-icon {
    color: #217346;
}

.export-option-card:hover .option-icon {
    transform: scale(1.1);
}

.option-content h4 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: bold;
}

.option-content p {
    color: #6c757d;
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

.option-content small {
    color: #868e96;
    font-size: 12px;
    display: block;
    margin-top: 8px;
    font-style: italic;
}

.option-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.pdf-option .option-badge {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.word-option .option-badge {
    background: linear-gradient(135deg, #2b579a, #1e3d72);
}

.excel-option .option-badge {
    background: linear-gradient(135deg, #217346, #1a5d37);
}

/* Products Management Card - Modern Design */
.products-management-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 30px;
    color: white;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    grid-column: span 2;
    min-height: 320px;
}

.products-management-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
}

.products-management-card .card-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.products-management-card .header-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
}

.products-management-card .header-content h3 {
    font-size: 1.8rem;
    font-weight: 800;
    margin: 0 0 5px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.products-management-card .header-content p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.products-management-card .card-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 25px;
}

.products-management-card .stat-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.products-management-card .stat-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
}

.products-management-card .stat-number {
    font-size: 2rem;
    font-weight: 900;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.products-management-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 600;
}

.products-management-card .card-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 25px;
}

.products-management-card .action-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.products-management-card .action-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.products-management-card .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.products-management-card .capacity-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    font-weight: 600;
}

.products-management-card .navigate-arrow {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.products-management-card:hover .navigate-arrow {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(-5px);
}

.products-management-card .card-decoration {
    position: absolute;
    top: -30px;
    left: -30px;
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    opacity: 0.3;
    transition: all 0.3s ease;
}

.products-management-card:hover .card-decoration {
    opacity: 0.5;
    transform: scale(1.1) rotate(10deg);
}

/* Responsive Design for Products Management Card */
@media (max-width: 1200px) {
    .products-management-card {
        grid-column: span 1;
        min-height: 280px;
    }

    .products-management-card .card-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .products-management-card .card-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .products-management-card {
        padding: 20px;
        min-height: 250px;
    }

    .products-management-card .header-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .products-management-card .header-content h3 {
        font-size: 1.4rem;
    }

    .products-management-card .card-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .products-management-card .card-actions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .products-management-card .stat-number {
        font-size: 1.5rem;
    }

    .products-management-card .action-item {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
}

/* Enhanced Dashboard Stat Cards with Modern Colors */
.stat-card {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    padding: 25px;
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 2px solid transparent;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stat-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.1;
    transition: all 0.3s ease;
}

.decoration-icon {
    font-size: 40px;
}

.stat-card:hover .stat-decoration {
    opacity: 0.2;
    transform: scale(1.1) rotate(10deg);
}

/* Blue Theme - Total Products */
.stat-card-blue::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.stat-card-blue .stat-decoration {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card-blue:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.stat-card-blue:hover .stat-content h3 {
    color: #1e3a8a;
    text-shadow: 0 1px 3px rgba(30, 58, 138, 0.2);
}

.stat-card-blue:hover .stat-content p {
    color: #1e40af;
}

.stat-card-blue .click-indicator {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

/* Unified text styling for blue card to match other cards */

/* Purple Theme - Total Customers */
.stat-card-purple::before {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-card-purple .stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.stat-card-purple .stat-decoration {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-card-purple:hover {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
}

.stat-card-purple:hover .stat-content h3 {
    color: #581c87;
    text-shadow: 0 1px 3px rgba(88, 28, 135, 0.2);
}

.stat-card-purple:hover .stat-content p {
    color: #6b21a8;
}

.stat-card-purple .click-indicator {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Green Theme - Available Products */
.stat-card-green::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.stat-card-green .stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.stat-card-green .stat-decoration {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card-green:hover {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
}

.stat-card-green:hover .stat-content h3 {
    color: #064e3b;
    text-shadow: 0 1px 3px rgba(6, 78, 59, 0.2);
}

.stat-card-green:hover .stat-content p {
    color: #047857;
}

.stat-card-green .click-indicator {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Orange Theme - Reserved Products */
.stat-card-orange::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card-orange .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.stat-card-orange .stat-decoration {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card-orange:hover {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.stat-card-orange:hover .stat-content h3 {
    color: #92400e;
    text-shadow: 0 1px 3px rgba(146, 64, 14, 0.2);
}

.stat-card-orange:hover .stat-content p {
    color: #b45309;
}

.stat-card-orange .click-indicator {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Teal Theme - Breeding Requests */
.stat-card-teal::before {
    background: linear-gradient(90deg, #14b8a6, #0d9488);
}

.stat-card-teal .stat-icon {
    background: linear-gradient(135deg, #14b8a6, #0d9488);
    color: white;
}

.stat-card-teal .stat-decoration {
    background: linear-gradient(135deg, #14b8a6, #0d9488);
}

.stat-card-teal:hover {
    border-color: #14b8a6;
    background: linear-gradient(135deg, #f0fdfa, #ccfbf1);
}

.stat-card-teal:hover .stat-content h3 {
    color: #134e4a;
    text-shadow: 0 1px 3px rgba(19, 78, 74, 0.2);
}

.stat-card-teal:hover .stat-content p {
    color: #0f766e;
}

.stat-card-teal .click-indicator {
    background: linear-gradient(135deg, #14b8a6, #0d9488);
}

/* Pink Theme - Production Requests */
.stat-card-pink::before {
    background: linear-gradient(90deg, #ec4899, #db2777);
}

.stat-card-pink .stat-icon {
    background: linear-gradient(135deg, #ec4899, #db2777);
    color: white;
}

.stat-card-pink .stat-decoration {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.stat-card-pink:hover {
    border-color: #ec4899;
    background: linear-gradient(135deg, #fdf2f8, #fce7f3);
}

.stat-card-pink:hover .stat-content h3 {
    color: #831843;
    text-shadow: 0 1px 3px rgba(131, 24, 67, 0.2);
}

.stat-card-pink:hover .stat-content p {
    color: #be185d;
}

.stat-card-pink .click-indicator {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

/* Theme Settings Styles */
.theme-preview {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background: #f8fafc;
}

.preview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.preview-header {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2d3748;
}

.preview-stats {
    display: flex;
    gap: 15px;
}

.preview-stat {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 10px;
    flex: 1;
}

.preview-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.preview-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.preview-content p {
    font-size: 0.9rem;
    margin: 0;
    color: #718096;
}

/* Enhanced Welcome Section */
.welcome-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 30px;
    padding: 50px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.2);
    transform: perspective(1000px) rotateX(2deg);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: welcomeCardFloat 6s ease-in-out infinite alternate;
}

.welcome-card:hover {
    transform: perspective(1000px) rotateX(0deg) translateY(-10px);
    box-shadow: 0 30px 70px rgba(102, 126, 234, 0.5);
}

@keyframes welcomeCardFloat {
    0% {
        transform: perspective(1000px) rotateX(2deg) translateY(0px);
    }
    100% {
        transform: perspective(1000px) rotateX(2deg) translateY(-5px);
    }
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
    animation: backgroundShimmer 8s ease-in-out infinite alternate;
    z-index: 1;
}

.welcome-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255,255,255,0.1) 50%,
        transparent 70%);
    animation: welcomeShine 4s ease-in-out infinite;
    z-index: 2;
}

@keyframes backgroundShimmer {
    0% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    100% {
        transform: rotate(180deg) scale(1.1);
        opacity: 0.6;
    }
}

@keyframes welcomeShine {
    0%, 100% {
        transform: translateX(-100%) skewX(-15deg);
        opacity: 0;
    }
    50% {
        transform: translateX(100%) skewX(-15deg);
        opacity: 1;
    }
}

.welcome-header {
    display: flex;
    align-items: center;
    gap: 25px;
    margin-bottom: 30px;
    position: relative;
    z-index: 10;
    position: relative;
    z-index: 2;
}

.welcome-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.welcome-icon:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
}

/* Welcome Dashboard Icon */
.welcome-dashboard-icon {
    width: 40px;
    height: 40px;
    transition: all 0.3s ease;
}

.welcome-icon:hover .welcome-dashboard-icon {
    transform: scale(1.1);
}

.welcome-content h3 {
    font-size: 2.5rem;
    margin: 0 0 15px 0;
    font-weight: 900;
    text-shadow: 3px 3px 8px rgba(0,0,0,0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 10;
    animation: welcomeGlow 3s ease-in-out infinite alternate;
}

.welcome-content h3::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(240,249,255,0.2));
    border-radius: 15px;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.7;
}

.welcome-content p {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.95;
    line-height: 1.8;
    font-weight: 600;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 10;
}

.welcome-content p::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%);
    border-radius: 2px;
    animation: underlineGlow 2s ease-in-out infinite alternate;
}

@keyframes welcomeGlow {
    0% {
        filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
    }
    100% {
        filter: drop-shadow(0 0 20px rgba(255,255,255,0.6));
    }
}

@keyframes underlineGlow {
    0% {
        opacity: 0.3;
        transform: scaleX(0.8);
    }
    100% {
        opacity: 0.8;
        transform: scaleX(1);
    }
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
    position: relative;
    z-index: 2;
}

/* Gradient Buttons */
.btn-gradient-blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-gradient-purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border: none;
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.btn-gradient-green {
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.btn-gradient-blue:hover,
.btn-gradient-purple:hover,
.btn-gradient-green:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.2);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn-gradient-blue:hover .btn-shine,
.btn-gradient-purple:hover .btn-shine,
.btn-gradient-green:hover .btn-shine {
    left: 100%;
}

/* System Features */
.system-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 30px;
    position: relative;
    z-index: 2;
}

.feature-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
    color: rgba(255, 255, 255, 0.9);
}

.feature-item span {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
}

/* Form Help Text */
.form-help {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
    line-height: 1.4;
}

/* Readonly Input Styling */
input[readonly] {
    background-color: #f8f9fa !important;
    border-color: #e9ecef !important;
    color: #495057 !important;
    cursor: not-allowed;
}

input[readonly]:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
    border-color: #6c757d !important;
}

/* Phone Input Container */
.phone-input-container {
    display: flex;
    align-items: center;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.phone-input-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.country-code {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px 15px;
    font-weight: 600;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
    border-right: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.phone-input-container input {
    border: none !important;
    border-radius: 0 !important;
    flex: 1;
    padding: 12px 15px;
    font-size: 14px;
    background: transparent;
    outline: none;
}

.phone-input-container input:focus {
    box-shadow: none !important;
    border: none !important;
}

.phone-input-container input::placeholder {
    color: #6c757d;
    opacity: 0.7;
}

/* ===== SPLASH SCREEN STYLES ===== */
.splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 1;
    visibility: visible;
    transition: all 0.8s ease-in-out;
    font-family: 'Cairo', sans-serif;
}

.splash-screen.fade-out {
    opacity: 0;
    visibility: hidden;
    transform: scale(1.05);
}

.splash-container {
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 40px;
    animation: splashFadeIn 1s ease-out;
}

@keyframes splashFadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Splash Header */
.splash-header {
    margin-bottom: 40px;
    animation: headerSlideDown 1.2s ease-out 0.3s both;
}

@keyframes headerSlideDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.splash-logo {
    margin-bottom: 20px;
}

.splash-logo-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    filter: drop-shadow(0 5px 15px rgba(0,0,0,0.3));
    animation: logoFloat 3s ease-in-out infinite;
}

.splash-logo-icon.custom-logo {
    object-fit: contain;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.splash-logo-icon.default-logo {
    /* Default SVG logo styles */
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.splash-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 15px 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.splash-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 400;
    margin: 0;
}

/* Loading Section */
.splash-loading {
    margin: 40px 0;
    animation: loadingFadeIn 1.5s ease-out 0.6s both;
}

@keyframes loadingFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #ffd700;
    border-radius: 50%;
    margin: 0 auto 15px auto;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
    animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes textPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Enter Button Section */
.splash-enter-section {
    margin: 40px 0 30px 0;
    animation: enterButtonFadeIn 1.8s ease-out 1s both;
}

@keyframes enterButtonFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.enter-app-btn {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border: none;
    padding: 15px 35px;
    border-radius: 30px;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow:
        0 6px 20px rgba(255, 215, 0, 0.4),
        0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 180px;
    justify-content: center;
    font-family: 'Cairo', sans-serif;
}

.enter-app-btn:hover {
    background: linear-gradient(135deg, #ffed4e, #ffc947);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 10px 30px rgba(255, 215, 0, 0.6),
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.enter-app-btn:active {
    transform: translateY(0) scale(1);
    box-shadow:
        0 3px 10px rgba(255, 215, 0, 0.4),
        0 1px 5px rgba(0, 0, 0, 0.2);
}

.enter-app-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.enter-app-btn:hover i {
    transform: translateX(3px);
}

/* Developer Section */
.splash-developer-section {
    margin: 30px 0;
    animation: developerSectionFadeIn 2s ease-out 1s both;
}

@keyframes developerSectionFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.developer-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 15px;
    max-width: 350px;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.developer-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.developer-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: #333;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    animation: avatarPulse 2s ease-in-out infinite;
    flex-shrink: 0;
}

@keyframes avatarPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 18px rgba(255, 215, 0, 0.5);
    }
}

.developer-details {
    text-align: right;
    flex: 1;
}

.developer-details h4 {
    margin: 0 0 8px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.developer-name {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

/* Footer */
.splash-footer {
    margin-top: 25px;
    animation: footerFadeIn 2.2s ease-out 1.4s both;
}

@keyframes footerFadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.version-info,
.copyright-info {
    font-size: 0.75rem;
    opacity: 0.6;
    margin: 3px 0;
    text-align: center;
}

.version-info {
    font-weight: 500;
    color: #e0e0e0;
}

.copyright-info {
    font-style: italic;
    color: #d0d0d0;
}

/* Hide loading when enter button shows */
.splash-screen.show-enter .splash-loading {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.5s ease;
}

.splash-screen.show-enter .splash-enter-section {
    animation: none;
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Responsive Design for Exit Button */
@media (max-width: 768px) {
    .header-actions {
        gap: 10px;
    }

    .exit-app-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
        min-width: 80px;
    }

    .exit-app-btn span {
        display: none;
    }

    .exit-app-btn i {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .exit-app-btn {
        padding: 8px 12px;
        min-width: 60px;
    }
}

/* Responsive Design for Splash Screen */
@media (max-width: 768px) {
    .splash-container {
        padding: 30px 20px;
        max-width: 90%;
    }

    .splash-title {
        font-size: 1.6rem;
    }

    .splash-subtitle {
        font-size: 1rem;
    }

    .splash-logo-icon {
        width: 60px;
        height: 60px;
    }

    .enter-app-btn {
        padding: 12px 25px;
        font-size: 0.9rem;
        min-width: 150px;
    }

    .developer-info,
    .version-info {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .splash-container {
        padding: 20px 15px;
    }

    .splash-title {
        font-size: 1.4rem;
        line-height: 1.3;
    }

    .splash-subtitle {
        font-size: 0.9rem;
    }

    .splash-logo-icon {
        width: 50px;
        height: 50px;
    }

    .loading-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
    }

    .enter-app-btn {
        padding: 10px 20px;
        font-size: 0.85rem;
        min-width: 130px;
    }

    .developer-card {
        padding: 12px;
        max-width: 250px;
        gap: 10px;
    }

    .developer-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .developer-details h4 {
        font-size: 0.8rem;
        margin-bottom: 5px;
    }

    .developer-name {
        font-size: 1rem;
    }

    .version-info,
    .copyright-info {
        font-size: 0.7rem;
    }
}





/* Enter Button Section */
.splash-enter-section {
    margin-top: 40px;
    text-align: center;
    animation: enterSectionFadeIn 3s ease-out 2s both;
}

@keyframes enterSectionFadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.enter-app-btn {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    color: #333;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    box-shadow:
        0 8px 25px rgba(255, 215, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 200px;
    justify-content: center;
    animation: enterBtnPulse 2s ease-in-out infinite;
}

@keyframes enterBtnPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 8px 25px rgba(255, 215, 0, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 12px 35px rgba(255, 215, 0, 0.6),
            0 6px 18px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

.enter-app-btn:hover {
    background: linear-gradient(135deg, #ffed4e, #ffc947);
    transform: translateY(-3px) scale(1.08);
    box-shadow:
        0 15px 40px rgba(255, 215, 0, 0.6),
        0 8px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    animation: none;
}

.enter-app-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 5px 15px rgba(255, 215, 0, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.enter-app-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.enter-app-btn:hover i {
    transform: translateX(5px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.enter-app-btn:hover .btn-shine {
    left: 100%;
}

.enter-note {
    margin-top: 15px;
    font-size: 0.9rem;
    opacity: 0.8;
    color: #ffffff;
    font-style: italic;
    animation: enterNoteFade 1.5s ease-in-out infinite alternate;
}

@keyframes enterNoteFade {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* Hide loading section when enter button is shown */
.splash-screen.show-enter .splash-loading {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.5s ease;
}

.splash-screen.show-enter .splash-enter-section {
    animation: none;
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Responsive Design for Splash Screen */
@media (max-width: 768px) {
    .splash-content {
        padding: 20px;
        max-width: 90%;
    }

    .splash-company-name {
        font-size: 1.8rem;
    }

    .splash-company-subtitle {
        font-size: 1rem;
    }

    .splash-company-details {
        flex-direction: column;
        gap: 15px;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .developer-info {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .loading-bar {
        width: 250px;
    }

    .enter-app-btn {
        padding: 15px 30px;
        font-size: 1rem;
        min-width: 180px;
    }

    .enter-note {
        font-size: 0.8rem;
        margin-top: 12px;
    }

    .developer-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        padding: 15px;
        max-width: 280px;
    }

    .developer-details {
        text-align: center;
    }

    .developer-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .developer-details h4 {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .developer-name {
        font-size: 1.1rem;
    }
}

/* Enhanced Form Sections */
.form-section {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h4::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Enhanced Modal Styles */
.modal-content {
    max-height: 85vh;
    overflow-y: auto;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 25px;
}

/* Enhanced Button Styles */
.btn-sm {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 6px;
}

.btn-secondary.btn-sm {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border: none;
    color: white;
    margin-top: 10px;
}

.btn-secondary.btn-sm:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-1px);
}

/* Enhanced Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .dynamic-item {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-remove {
        align-self: center;
        margin-top: 5px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 10px;
        max-height: 95vh;
    }

    .modal-body {
        max-height: 75vh;
        padding: 15px;
    }
}

/* Enhanced Scrollbar for Modal */
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* Theme Variations */
/* Ocean Theme */
body.theme-ocean {
    background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
}

body.theme-ocean .welcome-card {
    background: linear-gradient(135deg, #0ea5e9, #06b6d4);
}

body.theme-ocean .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
}

body.theme-ocean .stat-card-blue::before {
    background: linear-gradient(90deg, #0ea5e9, #0284c7);
}

body.theme-ocean .preview-icon {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
}

body.theme-ocean .stat-card-blue .stat-content h3 {
    color: #0c2340;
    text-shadow: 0 2px 4px rgba(12, 35, 64, 0.25);
}

body.theme-ocean .stat-card-blue .stat-content p {
    color: #1e3a5f;
    text-shadow: 0 1px 3px rgba(30, 58, 95, 0.2);
}

/* Sunset Theme */
body.theme-sunset {
    background: linear-gradient(135deg, #f97316 0%, #ec4899 100%);
}

body.theme-sunset .welcome-card {
    background: linear-gradient(135deg, #f97316, #ec4899);
}

body.theme-sunset .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

body.theme-sunset .stat-card-blue::before {
    background: linear-gradient(90deg, #f97316, #ea580c);
}

body.theme-sunset .preview-icon {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

body.theme-sunset .stat-card-blue .stat-content h3 {
    color: #7c2d12;
    text-shadow: 0 2px 4px rgba(124, 45, 18, 0.25);
}

body.theme-sunset .stat-card-blue .stat-content p {
    color: #9a3412;
    text-shadow: 0 1px 3px rgba(154, 52, 18, 0.2);
}

/* Forest Theme */
body.theme-forest {
    background: linear-gradient(135deg, #059669 0%, #1e40af 100%);
}

body.theme-forest .welcome-card {
    background: linear-gradient(135deg, #059669, #1e40af);
}

body.theme-forest .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #059669, #047857);
}

body.theme-forest .stat-card-blue::before {
    background: linear-gradient(90deg, #059669, #047857);
}

body.theme-forest .preview-icon {
    background: linear-gradient(135deg, #059669, #047857);
}

/* Royal Theme */
body.theme-royal {
    background: linear-gradient(135deg, #7c3aed 0%, #f59e0b 100%);
}

body.theme-royal .welcome-card {
    background: linear-gradient(135deg, #7c3aed, #f59e0b);
}

body.theme-royal .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

body.theme-royal .stat-card-blue::before {
    background: linear-gradient(90deg, #7c3aed, #6d28d9);
}

body.theme-royal .preview-icon {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

/* Dark Theme */
body.theme-dark {
    background: linear-gradient(135deg, #374151 0%, #111827 100%);
}

body.theme-dark .welcome-card {
    background: linear-gradient(135deg, #374151, #111827);
}

body.theme-dark .stat-card {
    background: rgba(31, 41, 55, 0.95);
    color: #f9fafb;
}

body.theme-dark .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #374151, #1f2937);
}

body.theme-dark .stat-card-blue::before {
    background: linear-gradient(90deg, #374151, #1f2937);
}

body.theme-dark .preview-icon {
    background: linear-gradient(135deg, #374151, #1f2937);
}

body.theme-dark .stat-card-blue .stat-content h3 {
    color: #f8fafc;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

body.theme-dark .stat-card-blue .stat-content p {
    color: #e2e8f0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

body.theme-dark .stat-content h3 {
    color: #f8fafc;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

body.theme-dark .stat-content p {
    color: #e2e8f0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* Professional Theme */
body.theme-professional {
    background: linear-gradient(135deg, #1e40af 0%, #64748b 100%);
}

body.theme-professional .welcome-card {
    background: linear-gradient(135deg, #1e40af, #64748b);
}

body.theme-professional .stat-card-blue .stat-icon {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
}

body.theme-professional .stat-card-blue::before {
    background: linear-gradient(90deg, #1e40af, #1e3a8a);
}

body.theme-professional .preview-icon {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
}

/* Animation for Dynamic Items */
.dynamic-item {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fixed Footer */
.fixed-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 8px 0;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    border-top: 2px solid #667eea;
}

.copyright-text {
    text-align: center;
    font-size: 0.85rem;
    line-height: 1.4;
    padding: 0 20px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    pointer-events: none;
}

.copyright-text strong {
    color: #ffd700;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.phone-numbers {
    color: #e8f4fd;
    font-weight: 600;
    direction: ltr;
    unicode-bidi: bidi-override;
    display: inline-block;
    margin: 0 5px;
}

/* ===== LICENSE ACTIVATION STYLES ===== */
.license-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: 'Cairo', sans-serif;
}

.license-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: center;
    animation: licenseSlideIn 0.8s ease-out;
}

@keyframes licenseSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.license-header {
    margin-bottom: 30px;
}

.license-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.license-logo i {
    font-size: 35px;
    color: white;
}

.license-header h1 {
    font-size: 28px;
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.license-header p {
    color: #7f8c8d;
    margin: 5px 0;
    font-size: 14px;
}

.company-name {
    color: #667eea !important;
    font-weight: 600 !important;
    font-size: 16px !important;
}

.license-form {
    margin-bottom: 30px;
}

.activation-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.code-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.activation-input {
    flex: 1;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px;
    text-align: center;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    transition: all 0.3s ease;
}

.activation-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.activate-btn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.activate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.activate-btn:active {
    transform: translateY(0);
}

.code-info {
    text-align: right;
    font-size: 12px;
    color: #6c757d;
}

.code-info p {
    margin: 5px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.license-status {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
}

.license-status h3 {
    color: #28a745;
    margin-bottom: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.status-info {
    text-align: right;
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
    border-bottom: none;
}

.status-item .label {
    font-weight: 600;
    color: #495057;
}

.status-item .value {
    color: #667eea;
    font-weight: 600;
}

.enter-app-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.enter-app-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.license-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}

.error-message {
    color: #721c24;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.license-footer {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
    font-size: 12px;
    color: #6c757d;
}

.license-footer p {
    margin: 5px 0;
}

/* Responsive Design for License Screen */
@media (max-width: 768px) {
    .license-container {
        padding: 30px 20px;
        margin: 20px;
    }

    .license-header h1 {
        font-size: 24px;
    }

    .code-input-container {
        flex-direction: column;
    }

    .activation-input {
        margin-bottom: 10px;
    }
}

/* License Management Styles */
.license-management {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.license-management .btn {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.license-info {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.license-info small {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 12px;
}

/* Form Validation Styles */
.field-error {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.form-control:disabled {
    background-color: #f8f9fa !important;
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    border-color: #dee2e6 !important;
}

.form-control:disabled::placeholder {
    color: #adb5bd !important;
}

.form-control.error {
    border-color: #dc3545 !important;
    background-color: #fff5f5 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-control.success {
    border-color: #28a745 !important;
    background-color: #f8fff8 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.form-control.enabled {
    border-color: #28a745 !important;
    transition: all 0.3s ease !important;
}

.form-group {
    position: relative;
}

.field-error {
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    z-index: 10;
}

/* Ensure form groups have enough space for error messages */
.form-grid .form-group {
    margin-bottom: 25px;
}

/* Disabled field tooltip */
.form-control:disabled {
    position: relative;
}

.form-control:disabled:hover::after {
    content: "يجب ملء الحقول السابقة أولاً";
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

.form-control:disabled:hover::before {
    content: "";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
    pointer-events: none;
}

/* Delete All Products Button */
.btn-delete-all {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-delete-all:hover {
    background: linear-gradient(135deg, #c82333, #bd2130) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4) !important;
}

.btn-delete-all:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
}

.btn-delete-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-delete-all:hover::before {
    left: 100%;
}

/* Delete confirmation modal styles */
.delete-confirmation-modal {
    background: rgba(0, 0, 0, 0.8) !important;
}

.delete-confirmation-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    margin: auto;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: deleteModalSlideIn 0.3s ease-out;
}

@keyframes deleteModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.delete-warning-icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 20px;
    animation: warningPulse 1s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.delete-confirmation-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc3545;
    margin-bottom: 15px;
}

.delete-confirmation-text {
    color: #6c757d;
    margin-bottom: 25px;
    line-height: 1.6;
}

.delete-confirmation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-confirm-delete {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-confirm-delete:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
}

.btn-cancel-delete {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel-delete:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
}

/* Header Actions Styling */
.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.header-actions .btn {
    white-space: nowrap;
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .header-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===== DISMANTLE NOTIFICATIONS STYLES ===== */
.dismantle-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 400px;
    max-width: 90vw;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    animation: slideInRight 0.5s ease-out;
    border-left: 6px solid;
    overflow: hidden;
}

/* Notification types */
.dismantle-urgent {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5, #ffffff);
    animation: urgentPulse 1s ease-in-out infinite;
}

.dismantle-danger {
    border-left-color: #fd7e14;
    background: linear-gradient(135deg, #fff8f0, #ffffff);
    animation: dangerBlink 2s ease-in-out infinite;
}

.dismantle-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fffbf0, #ffffff);
    animation: warningGlow 3s ease-in-out infinite;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes urgentPulse {
    0%, 100% {
        box-shadow: 0 20px 60px rgba(220, 53, 69, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 25px 80px rgba(220, 53, 69, 0.6);
        transform: scale(1.02);
    }
}

@keyframes dangerBlink {
    0%, 100% {
        box-shadow: 0 20px 60px rgba(253, 126, 20, 0.3);
    }
    50% {
        box-shadow: 0 25px 80px rgba(253, 126, 20, 0.7);
    }
}

@keyframes warningGlow {
    0%, 100% {
        box-shadow: 0 20px 60px rgba(255, 193, 7, 0.2);
    }
    50% {
        box-shadow: 0 25px 80px rgba(255, 193, 7, 0.5);
    }
}

.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    gap: 15px;
}

.notification-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.dismantle-urgent .notification-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
    animation: iconSpin 2s linear infinite;
}

.dismantle-danger .notification-icon {
    background: linear-gradient(135deg, #fd7e14, #e8690b);
    animation: iconBounce 1s ease-in-out infinite;
}

.dismantle-warning .notification-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.notification-text {
    flex: 1;
}

.notification-text h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

.notification-text p {
    margin: 4px 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
}

.btn-view-product,
.btn-close-notification {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    min-width: 80px;
}

.btn-view-product {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-view-product:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-close-notification {
    background: #6c757d;
    color: white;
}

.btn-close-notification:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Fade out animation */
.dismantle-notification.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Multiple notifications stacking */
.dismantle-notification:nth-child(n+2) {
    top: calc(20px + (120px * var(--notification-index, 0)));
}

/* Responsive design */
@media (max-width: 768px) {
    .dismantle-notification {
        width: 350px;
        right: 10px;
        top: 10px;
    }

    .notification-content {
        padding: 15px;
        gap: 10px;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .notification-text h4 {
        font-size: 14px;
    }

    .notification-text p {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .dismantle-notification {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
    }

    .notification-actions {
        flex-direction: row;
    }

    .btn-view-product,
    .btn-close-notification {
        flex: 1;
        min-width: auto;
    }
}

/* Dismantle Indicators in Table */
.dismantle-indicator {
    display: inline-block;
    font-size: 16px;
    margin-right: 5px;
    animation: indicatorPulse 2s ease-in-out infinite;
}

.dismantle-indicator.overdue {
    animation: urgentBlink 1s ease-in-out infinite;
}

.dismantle-indicator.today {
    animation: todayPulse 1.5s ease-in-out infinite;
}

.dismantle-indicator.warning {
    animation: warningGlow 2s ease-in-out infinite;
}

@keyframes indicatorPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes urgentBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

@keyframes todayPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* Table row highlighting */
.row-overdue {
    background: linear-gradient(90deg, rgba(220, 53, 69, 0.1), transparent) !important;
    border-left: 4px solid #dc3545 !important;
}

.row-today {
    background: linear-gradient(90deg, rgba(253, 126, 20, 0.1), transparent) !important;
    border-left: 4px solid #fd7e14 !important;
}

.row-warning {
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.1), transparent) !important;
    border-left: 4px solid #ffc107 !important;
}

.row-overdue:hover,
.row-today:hover,
.row-warning:hover {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.05), transparent) !important;
}

/* ===== FLOATING CALCULATOR STYLES ===== */
.floating-calculator {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    font-family: 'Cairo', sans-serif;
    overflow: hidden;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateY(-50%) translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
}

.calculator-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.calculator-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-calculator {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-calculator:hover {
    background: rgba(255, 255, 255, 0.2);
}

.calculator-body {
    padding: 20px;
}

.calculator-display {
    margin-bottom: 15px;
}

.calculator-display input {
    width: 100%;
    height: 50px;
    font-size: 24px;
    text-align: right;
    border: none;
    border-radius: 8px;
    padding: 0 15px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-weight: 600;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

.calculator-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.calculator-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.calc-btn {
    height: 50px;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.calc-number {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: #333;
}

.calc-number:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transform: translateY(-1px);
}

.calc-operator {
    background: linear-gradient(135deg, #ffc107, #ffb300);
    color: white;
}

.calc-operator:hover {
    background: linear-gradient(135deg, #ffb300, #ff8f00);
    transform: translateY(-1px);
}

.calc-equals {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    grid-column: span 2;
}

.calc-equals:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-1px);
}

.calc-clear {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.calc-clear:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-1px);
}

.calc-zero {
    grid-column: span 2;
}

.calc-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .fixed-footer,
    .btn,
    .modal,
    .floating-calculator {
        display: none !important;
    }

    .main {
        padding: 0;
        padding-bottom: 0;
    }

    .table-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .data-table th,
    .data-table td {
        border: 1px solid #000;
        padding: 8px;
    }
}

/* Responsive Design for Calculator */
@media (max-width: 768px) {
    .floating-calculator {
        right: 10px;
        width: 260px;
    }
}

/* ==========================================
   Mobile-First Responsive Design
   ========================================== */

/* General Mobile Optimizations */
@media (max-width: 768px) {
    body {
        font-size: 14px;
        line-height: 1.5;
    }

    .container {
        padding: 0 15px;
    }

    .main {
        padding: 20px 0 40px 0;
    }

    .section-header h2,
    .dashboard-header h2 {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 15px;
    }

    /* Hide less important elements on mobile */
    .hide-mobile {
        display: none !important;
    }

    /* Improve touch targets */
    .clickable-row,
    .stat-card.clickable,
    .action-btn {
        min-height: 48px;
    }

    /* Improve text readability */
    .data-table td,
    .stat-content p,
    .form-group label {
        font-size: 14px;
    }

    /* Optimize spacing */
    .stats-grid,
    .actions-grid,
    .filters-section {
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 13px;
    }

    .container {
        padding: 0 10px;
    }

    .main {
        padding: 15px 0 30px 0;
    }

    .section-header h2,
    .dashboard-header h2 {
        font-size: 1.5rem;
    }

    /* Ultra-compact mode for very small screens */
    .ultra-compact {
        padding: 8px;
        margin: 5px 0;
        font-size: 12px;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    /* Increase touch targets */
    .btn,
    .nav-link,
    .search-clear,
    .close {
        min-height: 44px;
        min-width: 44px;
    }

    /* Remove hover effects that don't work on touch */
    .stat-card:hover,
    .product-row:hover,
    .document-item:hover {
        transform: none;
        box-shadow: inherit;
    }

    /* Improve button spacing */
    .form-actions,
    .export-buttons,
    .document-actions {
        gap: 12px;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 8px 0;
    }

    .header-content {
        flex-direction: row;
        justify-content: space-between;
    }

    .logo {
        order: 1;
    }

    .main-nav {
        order: 2;
        width: auto;
    }

    .header-actions {
        order: 3;
    }

    .main-nav ul {
        flex-direction: row;
        gap: 5px;
    }

    .nav-link {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-icon,
    .stat-icon,
    .document-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .header {
        background: rgba(44, 62, 80, 0.95);
    }

    .stat-card,
    .table-container,
    .filters-section {
        background: rgba(52, 73, 94, 0.95);
        color: #ecf0f1;
    }
}

/* End of Mobile Responsive Styles */

/* EMERGENCY FIX: Force show cloud storage */
.settings-card {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
    transform: none !important;
}

.settings-container {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

/* Force show settings when body has settings-active class */
body.settings-active .settings-card,
body.settings-active .settings-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
}

body.settings-active .settings-container {
    display: grid !important;
}

/* Override any hiding rules for cloud storage */
#settings .settings-card,
#settings .settings-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
}

#settings .settings-container {
    display: grid !important;
}
