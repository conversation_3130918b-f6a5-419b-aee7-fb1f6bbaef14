<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التخزين السحابي - النسور الماسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #117a8b);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-display {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .clear-log {
            background: #e53e3e;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-cloud"></i> اختبار أزرار نظام التخزين السحابي</h1>

        <!-- Connection Status -->
        <div class="test-section">
            <h2><i class="fas fa-wifi"></i> حالة الاتصال</h2>
            <div id="connectionStatus" class="status-display status-warning">
                <i class="fas fa-spinner fa-spin"></i> جاري فحص الاتصال...
            </div>
        </div>

        <!-- Google Drive Buttons -->
        <div class="test-section">
            <h2><i class="fab fa-google"></i> أزرار Google Drive</h2>
            
            <button class="btn btn-primary" onclick="testConnectToGoogleDrive()">
                <i class="fab fa-google"></i> تسجيل دخول Google
            </button>
            
            <button class="btn btn-success" onclick="testUploadAllToGoogleDrive()" disabled>
                <i class="fas fa-cloud-upload-alt"></i> رفع جميع البيانات
            </button>
            
            <button class="btn btn-info" onclick="testDownloadAllFromGoogleDrive()" disabled>
                <i class="fas fa-cloud-download-alt"></i> تحميل جميع البيانات
            </button>
            
            <button class="btn btn-warning" onclick="testCheckGoogleDriveConnection()">
                <i class="fas fa-wifi"></i> فحص الاتصال
            </button>
        </div>

        <!-- Auto Sync Buttons -->
        <div class="test-section">
            <h2><i class="fas fa-sync-alt"></i> المزامنة التلقائية</h2>
            
            <button class="btn btn-info" onclick="testPerformManualSync()">
                <i class="fas fa-sync-alt"></i> مزامنة يدوية فورية
            </button>
            
            <label style="margin: 10px;">
                <input type="checkbox" id="autoSyncTest" onchange="testToggleAutoSync()"> 
                تفعيل المزامنة التلقائية
            </label>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبار</h2>
            <button class="clear-log" onclick="clearLog()">مسح السجل</button>
            <div id="testLog" class="log-area"></div>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="google-drive-sync.js"></script>
    <script src="auto-sync-system.js"></script>

    <script>
        // Test Functions
        let testLog = document.getElementById('testLog');
        let connectionStatus = document.getElementById('connectionStatus');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testLog.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearLog() {
            testLog.innerHTML = '';
        }

        function updateConnectionStatus(connected, message) {
            if (connected) {
                connectionStatus.className = 'status-display status-success';
                connectionStatus.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                
                // Enable buttons
                document.querySelectorAll('.btn-success, .btn-info').forEach(btn => {
                    if (btn.onclick.toString().includes('GoogleDrive')) {
                        btn.disabled = false;
                    }
                });
            } else {
                connectionStatus.className = 'status-display status-error';
                connectionStatus.innerHTML = `<i class="fas fa-times-circle"></i> ${message}`;
                
                // Disable buttons
                document.querySelectorAll('.btn-success, .btn-info').forEach(btn => {
                    if (btn.onclick.toString().includes('GoogleDrive')) {
                        btn.disabled = true;
                    }
                });
            }
        }

        // Test function implementations
        async function testConnectToGoogleDrive() {
            log('🔐 اختبار تسجيل الدخول إلى Google Drive...');
            
            if (typeof connectToGoogleDrive === 'function') {
                try {
                    await connectToGoogleDrive();
                    log('✅ تم تسجيل الدخول بنجاح', 'success');
                    updateConnectionStatus(true, 'متصل بـ Google Drive');
                } catch (error) {
                    log('❌ فشل تسجيل الدخول: ' + error.message, 'error');
                    updateConnectionStatus(false, 'فشل في الاتصال');
                }
            } else {
                log('❌ وظيفة connectToGoogleDrive غير موجودة', 'error');
            }
        }

        async function testUploadAllToGoogleDrive() {
            log('📤 اختبار رفع جميع البيانات...');
            
            if (typeof uploadAllToGoogleDrive === 'function') {
                try {
                    await uploadAllToGoogleDrive();
                    log('✅ تم رفع البيانات بنجاح', 'success');
                } catch (error) {
                    log('❌ فشل رفع البيانات: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة uploadAllToGoogleDrive غير موجودة', 'error');
            }
        }

        async function testDownloadAllFromGoogleDrive() {
            log('📥 اختبار تحميل جميع البيانات...');
            
            if (typeof downloadAllFromGoogleDrive === 'function') {
                try {
                    await downloadAllFromGoogleDrive();
                    log('✅ تم تحميل البيانات بنجاح', 'success');
                } catch (error) {
                    log('❌ فشل تحميل البيانات: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة downloadAllFromGoogleDrive غير موجودة', 'error');
            }
        }

        async function testCheckGoogleDriveConnection() {
            log('🔍 اختبار فحص الاتصال...');
            
            if (typeof checkGoogleDriveConnection === 'function') {
                try {
                    const result = await checkGoogleDriveConnection();
                    if (result) {
                        log('✅ الاتصال يعمل بشكل صحيح', 'success');
                        updateConnectionStatus(true, 'الاتصال نشط');
                    } else {
                        log('⚠️ مشكلة في الاتصال', 'warning');
                        updateConnectionStatus(false, 'مشكلة في الاتصال');
                    }
                } catch (error) {
                    log('❌ خطأ في فحص الاتصال: ' + error.message, 'error');
                    updateConnectionStatus(false, 'خطأ في الاتصال');
                }
            } else {
                log('❌ وظيفة checkGoogleDriveConnection غير موجودة', 'error');
            }
        }

        async function testPerformManualSync() {
            log('🔄 اختبار المزامنة اليدوية...');
            
            if (typeof performManualSync === 'function') {
                try {
                    await performManualSync();
                    log('✅ تمت المزامنة بنجاح', 'success');
                } catch (error) {
                    log('❌ فشل في المزامنة: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة performManualSync غير موجودة', 'error');
            }
        }

        function testToggleAutoSync() {
            const checkbox = document.getElementById('autoSyncTest');
            log(`🔄 ${checkbox.checked ? 'تفعيل' : 'إيقاف'} المزامنة التلقائية...`);
            
            if (typeof toggleAutoSync === 'function') {
                try {
                    // Simulate the toggle
                    const autoSyncEnabled = document.createElement('input');
                    autoSyncEnabled.id = 'autoSyncEnabled';
                    autoSyncEnabled.type = 'checkbox';
                    autoSyncEnabled.checked = checkbox.checked;
                    document.body.appendChild(autoSyncEnabled);
                    
                    toggleAutoSync();
                    log(`✅ تم ${checkbox.checked ? 'تفعيل' : 'إيقاف'} المزامنة التلقائية`, 'success');
                    
                    document.body.removeChild(autoSyncEnabled);
                } catch (error) {
                    log('❌ خطأ في تبديل المزامنة التلقائية: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة toggleAutoSync غير موجودة', 'error');
            }
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار أزرار التخزين السحابي...');
            
            // Check if functions exist
            const functions = [
                'connectToGoogleDrive',
                'uploadAllToGoogleDrive', 
                'downloadAllFromGoogleDrive',
                'checkGoogleDriveConnection',
                'performManualSync',
                'toggleAutoSync'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    log(`✅ وظيفة ${func} موجودة`, 'success');
                } else {
                    log(`❌ وظيفة ${func} غير موجودة`, 'error');
                }
            });
            
            // Initial connection check
            setTimeout(() => {
                testCheckGoogleDriveConnection();
            }, 2000);
        });
    </script>
</body>
</html>
