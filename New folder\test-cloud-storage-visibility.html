<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رؤية نظام التخزين السحابي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .clear-log {
            background: #e53e3e;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .status-card {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border-color: #28a745;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .element-check {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 10px;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .element-check.found {
            background: #d4edda;
            border-color: #28a745;
        }

        .element-check.missing {
            background: #f8d7da;
            border-color: #dc3545;
        }

        .element-check.hidden {
            background: #fff3cd;
            border-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-cloud"></i> اختبار رؤية نظام التخزين السحابي</h1>

        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-tools"></i> أدوات الاختبار</h2>
            
            <button class="btn btn-primary" onclick="checkCloudStorageVisibility()">
                <i class="fas fa-search"></i> فحص رؤية التخزين السحابي
            </button>
            
            <button class="btn btn-success" onclick="forceShowCloudStorage()">
                <i class="fas fa-eye"></i> إجبار إظهار التخزين السحابي
            </button>
            
            <button class="btn btn-warning" onclick="simulateSettingsSection()">
                <i class="fas fa-cog"></i> محاكاة قسم الإعدادات
            </button>
            
            <button class="btn btn-danger" onclick="resetAllStyles()">
                <i class="fas fa-undo"></i> إعادة تعيين الأنماط
            </button>
        </div>

        <!-- Status Display -->
        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> حالة النظام</h2>
            <div id="statusDisplay"></div>
        </div>

        <!-- Element Checks -->
        <div class="test-section">
            <h2><i class="fas fa-list-check"></i> فحص العناصر</h2>
            <div id="elementChecks"></div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> سجل الاختبار</h2>
            <button class="clear-log" onclick="clearLog()">مسح السجل</button>
            <div id="testLog" class="log-area"></div>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="style.css" type="text/css"></script>
    <script src="script.js"></script>

    <script>
        let testLog = document.getElementById('testLog');
        let statusDisplay = document.getElementById('statusDisplay');
        let elementChecks = document.getElementById('elementChecks');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testLog.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearLog() {
            testLog.innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            statusDisplay.innerHTML = `<div class="status-card status-${type}">${message}</div>`;
        }

        function checkCloudStorageVisibility() {
            log('🔍 بدء فحص رؤية نظام التخزين السحابي...');
            
            // Check if settings section exists
            const settingsSection = document.getElementById('settings');
            if (!settingsSection) {
                log('❌ قسم الإعدادات غير موجود', 'error');
                updateStatus('❌ قسم الإعدادات غير موجود', 'error');
                return;
            }
            
            log('✅ قسم الإعدادات موجود', 'success');
            
            // Check if settings section is active
            const isActive = settingsSection.classList.contains('active');
            log(`📊 قسم الإعدادات نشط: ${isActive}`, isActive ? 'success' : 'warning');
            
            // Check body class
            const hasSettingsActive = document.body.classList.contains('settings-active');
            log(`📊 body يحتوي على settings-active: ${hasSettingsActive}`, hasSettingsActive ? 'success' : 'warning');
            
            // Check all settings cards
            const allSettingsCards = document.querySelectorAll('.settings-card');
            log(`📊 إجمالي بطاقات الإعدادات: ${allSettingsCards.length}`);
            
            // Check cloud storage card specifically
            const cloudStorageCards = Array.from(allSettingsCards).filter(card => {
                const header = card.querySelector('h3');
                return header && header.textContent.includes('التخزين السحابي');
            });
            
            log(`📊 بطاقات التخزين السحابي: ${cloudStorageCards.length}`);
            
            if (cloudStorageCards.length > 0) {
                cloudStorageCards.forEach((card, index) => {
                    const isVisible = card.offsetHeight > 0 && card.offsetWidth > 0;
                    const computedStyle = getComputedStyle(card);
                    const display = computedStyle.display;
                    const visibility = computedStyle.visibility;
                    const opacity = computedStyle.opacity;
                    
                    log(`📋 بطاقة التخزين السحابي #${index + 1}:`);
                    log(`   - مرئية: ${isVisible}`);
                    log(`   - display: ${display}`);
                    log(`   - visibility: ${visibility}`);
                    log(`   - opacity: ${opacity}`);
                    
                    if (isVisible && display !== 'none' && visibility !== 'hidden' && opacity !== '0') {
                        log(`✅ بطاقة التخزين السحابي #${index + 1} مرئية بشكل صحيح`, 'success');
                        updateStatus('✅ نظام التخزين السحابي مرئي', 'success');
                    } else {
                        log(`❌ بطاقة التخزين السحابي #${index + 1} مخفية`, 'error');
                        updateStatus('❌ نظام التخزين السحابي مخفي', 'error');
                    }
                });
            } else {
                log('❌ لم يتم العثور على بطاقة التخزين السحابي', 'error');
                updateStatus('❌ بطاقة التخزين السحابي غير موجودة', 'error');
            }
            
            updateElementChecks();
        }

        function updateElementChecks() {
            const elements = [
                { id: 'settings', name: 'قسم الإعدادات' },
                { selector: '.settings-card', name: 'بطاقات الإعدادات' },
                { selector: '.settings-container', name: 'حاوية الإعدادات' },
                { text: 'التخزين السحابي', name: 'بطاقة التخزين السحابي' }
            ];
            
            elementChecks.innerHTML = '';
            
            elements.forEach(element => {
                const div = document.createElement('div');
                div.className = 'element-check';
                
                let found = false;
                let visible = false;
                let count = 0;
                
                if (element.id) {
                    const el = document.getElementById(element.id);
                    found = !!el;
                    visible = found && el.offsetHeight > 0;
                    count = found ? 1 : 0;
                } else if (element.selector) {
                    const els = document.querySelectorAll(element.selector);
                    count = els.length;
                    found = count > 0;
                    visible = Array.from(els).some(el => el.offsetHeight > 0);
                } else if (element.text) {
                    const cards = Array.from(document.querySelectorAll('.settings-card')).filter(card => {
                        const header = card.querySelector('h3');
                        return header && header.textContent.includes(element.text);
                    });
                    count = cards.length;
                    found = count > 0;
                    visible = cards.some(card => card.offsetHeight > 0);
                }
                
                if (found && visible) {
                    div.classList.add('found');
                    div.innerHTML = `
                        <span><i class="fas fa-check"></i> ${element.name}</span>
                        <span style="color: #28a745; font-weight: bold;">موجود ومرئي (${count})</span>
                        <span style="color: #28a745;"><i class="fas fa-eye"></i></span>
                    `;
                } else if (found && !visible) {
                    div.classList.add('hidden');
                    div.innerHTML = `
                        <span><i class="fas fa-eye-slash"></i> ${element.name}</span>
                        <span style="color: #856404; font-weight: bold;">موجود لكن مخفي (${count})</span>
                        <span style="color: #856404;"><i class="fas fa-exclamation-triangle"></i></span>
                    `;
                } else {
                    div.classList.add('missing');
                    div.innerHTML = `
                        <span><i class="fas fa-times"></i> ${element.name}</span>
                        <span style="color: #dc3545; font-weight: bold;">غير موجود</span>
                        <span style="color: #dc3545;"><i class="fas fa-times-circle"></i></span>
                    `;
                }
                
                elementChecks.appendChild(div);
            });
        }

        function forceShowCloudStorage() {
            log('🔧 إجبار إظهار نظام التخزين السحابي...');
            
            // Add settings-active class to body
            document.body.classList.add('settings-active');
            log('✅ تم إضافة settings-active class للـ body');
            
            // Make settings section active
            const settingsSection = document.getElementById('settings');
            if (settingsSection) {
                settingsSection.classList.add('active');
                log('✅ تم تفعيل قسم الإعدادات');
            }
            
            // Force show all settings cards
            const settingsCards = document.querySelectorAll('.settings-card');
            settingsCards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.visibility = 'visible';
                card.style.opacity = '1';
                card.style.height = 'auto';
                card.style.overflow = 'visible';
                card.style.position = 'relative';
                card.style.left = 'auto';
                card.classList.remove('force-hidden');
                log(`✅ تم إظهار بطاقة إعدادات #${index + 1}`);
            });
            
            // Force show settings container
            const settingsContainer = document.querySelector('.settings-container');
            if (settingsContainer) {
                settingsContainer.style.display = 'grid';
                settingsContainer.style.visibility = 'visible';
                settingsContainer.style.opacity = '1';
                log('✅ تم إظهار حاوية الإعدادات');
            }
            
            log('✅ تم إجبار إظهار نظام التخزين السحابي', 'success');
            updateStatus('✅ تم إجبار إظهار نظام التخزين السحابي', 'success');
            
            setTimeout(() => {
                checkCloudStorageVisibility();
            }, 500);
        }

        function simulateSettingsSection() {
            log('🎭 محاكاة قسم الإعدادات...');
            
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Show settings section
            const settingsSection = document.getElementById('settings');
            if (settingsSection) {
                settingsSection.classList.add('active');
                log('✅ تم تفعيل قسم الإعدادات');
            }
            
            // Call ensureSettingsVisibility if available
            if (typeof ensureSettingsVisibility === 'function') {
                ensureSettingsVisibility();
                log('✅ تم استدعاء ensureSettingsVisibility');
            } else {
                log('⚠️ وظيفة ensureSettingsVisibility غير متاحة', 'warning');
            }
            
            setTimeout(() => {
                checkCloudStorageVisibility();
            }, 500);
        }

        function resetAllStyles() {
            log('🔄 إعادة تعيين جميع الأنماط...');
            
            // Reset body classes
            document.body.classList.remove('settings-active');
            document.documentElement.classList.remove('settings-cleaned');
            
            // Reset all settings elements
            const allSettings = document.querySelectorAll('.settings-card, .settings-container');
            allSettings.forEach(element => {
                element.style.cssText = '';
                element.classList.remove('force-hidden');
            });
            
            log('✅ تم إعادة تعيين جميع الأنماط', 'success');
            updateStatus('✅ تم إعادة تعيين جميع الأنماط', 'success');
            
            setTimeout(() => {
                checkCloudStorageVisibility();
            }, 500);
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار رؤية نظام التخزين السحابي...');
            
            setTimeout(() => {
                checkCloudStorageVisibility();
            }, 1000);
        });
    </script>
</body>
</html>
