<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار Google Drive - النسور الماسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #117a8b);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-display {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .clear-log {
            background: #e53e3e;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .function-check {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }

        .function-check.available {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .function-check.missing {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fab fa-google-drive"></i> اختبار أزرار Google Drive</h1>

        <!-- Function Availability Check -->
        <div class="test-section">
            <h2><i class="fas fa-code"></i> فحص توفر الوظائف</h2>
            <div id="functionChecks"></div>
            <button class="btn btn-info" onclick="checkFunctions()">
                <i class="fas fa-sync"></i> إعادة فحص الوظائف
            </button>
        </div>

        <!-- Connection Status -->
        <div class="test-section">
            <h2><i class="fas fa-wifi"></i> حالة الاتصال</h2>
            <div id="connectionStatus" class="status-display status-warning">
                <i class="fas fa-spinner fa-spin"></i> جاري فحص الاتصال...
            </div>
        </div>

        <!-- Google Drive Buttons -->
        <div class="test-section">
            <h2><i class="fab fa-google"></i> أزرار Google Drive</h2>
            
            <button class="btn btn-primary" onclick="testConnectToGoogleDrive()" id="connectBtn">
                <i class="fab fa-google"></i> تسجيل دخول Google
            </button>
            
            <button class="btn btn-success" onclick="testUploadAllToGoogleDrive()" disabled id="uploadBtn">
                <i class="fas fa-cloud-upload-alt"></i> رفع جميع البيانات
            </button>
            
            <button class="btn btn-info" onclick="testDownloadAllFromGoogleDrive()" disabled id="downloadBtn">
                <i class="fas fa-cloud-download-alt"></i> تحميل جميع البيانات
            </button>
            
            <button class="btn btn-warning" onclick="testCheckGoogleDriveConnection()" id="checkBtn">
                <i class="fas fa-wifi"></i> فحص الاتصال
            </button>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> سجل الاختبار</h2>
            <button class="clear-log" onclick="clearLog()">مسح السجل</button>
            <div id="testLog" class="log-area"></div>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="script.js"></script>
    <script src="google-drive-sync.js"></script>
    <script src="auto-sync-system.js"></script>

    <script>
        let testLog = document.getElementById('testLog');
        let connectionStatus = document.getElementById('connectionStatus');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testLog.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearLog() {
            testLog.innerHTML = '';
        }

        function updateConnectionStatus(connected, message) {
            if (connected) {
                connectionStatus.className = 'status-display status-success';
                connectionStatus.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                
                // Enable buttons
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('downloadBtn').disabled = false;
            } else {
                connectionStatus.className = 'status-display status-error';
                connectionStatus.innerHTML = `<i class="fas fa-times-circle"></i> ${message}`;
                
                // Disable buttons
                document.getElementById('uploadBtn').disabled = true;
                document.getElementById('downloadBtn').disabled = true;
            }
        }

        function checkFunctions() {
            const functions = [
                'connectToGoogleDrive',
                'uploadAllToGoogleDrive', 
                'downloadAllFromGoogleDrive',
                'checkGoogleDriveConnection',
                'performManualSync',
                'toggleAutoSync',
                'initializeGoogleDrive'
            ];
            
            const functionChecks = document.getElementById('functionChecks');
            functionChecks.innerHTML = '';
            
            functions.forEach(func => {
                const div = document.createElement('div');
                div.className = 'function-check';
                
                if (typeof window[func] === 'function') {
                    div.classList.add('available');
                    div.innerHTML = `
                        <span><i class="fas fa-check"></i> ${func}</span>
                        <span style="color: #28a745; font-weight: bold;">متوفرة</span>
                    `;
                    log(`✅ وظيفة ${func} متوفرة`, 'success');
                } else {
                    div.classList.add('missing');
                    div.innerHTML = `
                        <span><i class="fas fa-times"></i> ${func}</span>
                        <span style="color: #dc3545; font-weight: bold;">غير متوفرة</span>
                    `;
                    log(`❌ وظيفة ${func} غير متوفرة`, 'error');
                }
                
                functionChecks.appendChild(div);
            });
        }

        // Test function implementations
        async function testConnectToGoogleDrive() {
            log('🔐 اختبار تسجيل الدخول إلى Google Drive...');
            
            const btn = document.getElementById('connectBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاتصال...';
            
            try {
                if (typeof connectToGoogleDrive === 'function') {
                    await connectToGoogleDrive();
                    log('✅ تم تسجيل الدخول بنجاح', 'success');
                    updateConnectionStatus(true, 'متصل بـ Google Drive');
                } else {
                    throw new Error('وظيفة connectToGoogleDrive غير موجودة');
                }
            } catch (error) {
                log('❌ فشل تسجيل الدخول: ' + error.message, 'error');
                updateConnectionStatus(false, 'فشل في الاتصال');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fab fa-google"></i> تسجيل دخول Google';
            }
        }

        async function testUploadAllToGoogleDrive() {
            log('📤 اختبار رفع جميع البيانات...');
            
            const btn = document.getElementById('uploadBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';
            
            try {
                if (typeof uploadAllToGoogleDrive === 'function') {
                    await uploadAllToGoogleDrive();
                    log('✅ تم رفع البيانات بنجاح', 'success');
                } else {
                    throw new Error('وظيفة uploadAllToGoogleDrive غير موجودة');
                }
            } catch (error) {
                log('❌ فشل رفع البيانات: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> رفع جميع البيانات';
            }
        }

        async function testDownloadAllFromGoogleDrive() {
            log('📥 اختبار تحميل جميع البيانات...');
            
            const btn = document.getElementById('downloadBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            
            try {
                if (typeof downloadAllFromGoogleDrive === 'function') {
                    await downloadAllFromGoogleDrive();
                    log('✅ تم تحميل البيانات بنجاح', 'success');
                } else {
                    throw new Error('وظيفة downloadAllFromGoogleDrive غير موجودة');
                }
            } catch (error) {
                log('❌ فشل تحميل البيانات: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-cloud-download-alt"></i> تحميل جميع البيانات';
            }
        }

        async function testCheckGoogleDriveConnection() {
            log('🔍 اختبار فحص الاتصال...');
            
            const btn = document.getElementById('checkBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الفحص...';
            
            try {
                if (typeof checkGoogleDriveConnection === 'function') {
                    const result = await checkGoogleDriveConnection();
                    if (result) {
                        log('✅ الاتصال يعمل بشكل صحيح', 'success');
                        updateConnectionStatus(true, 'الاتصال نشط');
                    } else {
                        log('⚠️ مشكلة في الاتصال', 'warning');
                        updateConnectionStatus(false, 'مشكلة في الاتصال');
                    }
                } else {
                    throw new Error('وظيفة checkGoogleDriveConnection غير موجودة');
                }
            } catch (error) {
                log('❌ خطأ في فحص الاتصال: ' + error.message, 'error');
                updateConnectionStatus(false, 'خطأ في الاتصال');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-wifi"></i> فحص الاتصال';
            }
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار أزرار Google Drive...');
            
            // Check functions immediately
            checkFunctions();
            
            // Check connection after a delay
            setTimeout(() => {
                testCheckGoogleDriveConnection();
            }, 3000);
        });
    </script>
</body>
</html>
