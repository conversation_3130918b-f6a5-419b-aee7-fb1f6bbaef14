<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 اختبار Google Drive - النسور الماسية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
            transform: none;
        }
        
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 اختبار Google Drive - النسور الماسية</h1>
        
        <div id="status" class="status info">
            جاري التحقق من إعداد Google Drive...
        </div>
        
        <div>
            <button onclick="testConnection()">🔍 اختبار الاتصال</button>
            <button onclick="connectToGoogleDrive()" id="connectBtn">
                🔐 تسجيل دخول Google
            </button>
            <button onclick="testUpload()" id="uploadBtn" disabled>📤 اختبار الرفع</button>
            <button onclick="testDownload()" id="downloadBtn" disabled>📥 اختبار التحميل</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div id="log"></div>
    </div>

    <!-- Google Drive Sync -->
    <script src="google-drive-sync.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString('ar-SA')}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        async function testConnection() {
            log('🔍 اختبار الاتصال بـ Google Drive...', 'info');
            
            try {
                const result = await initializeGoogleDrive();
                if (result) {
                    log('✅ تم تهيئة Google Drive بنجاح', 'success');
                    updateStatus('Google Drive جاهز للاستخدام', 'success');
                    document.getElementById('connectBtn').disabled = false;
                } else {
                    log('❌ فشل في تهيئة Google Drive', 'error');
                    updateStatus('خطأ في إعداد Google Drive', 'error');
                }
            } catch (error) {
                log('❌ خطأ في الاتصال: ' + error.message, 'error');
                updateStatus('فشل في الاتصال', 'error');
            }
        }

        async function testUpload() {
            log('📤 اختبار رفع البيانات...', 'info');
            
            // Create test data
            window.products = [
                {
                    id: 'test-' + Date.now(),
                    name: 'منتج تجريبي',
                    price: 100,
                    quantity: 10,
                    category: 'اختبار',
                    timestamp: new Date().toISOString()
                }
            ];
            
            const result = await uploadProductsToDrive();
            if (result) {
                log('✅ تم رفع البيانات التجريبية بنجاح', 'success');
                updateStatus('تم اختبار الرفع بنجاح', 'success');
                document.getElementById('downloadBtn').disabled = false;
            } else {
                log('❌ فشل في رفع البيانات التجريبية', 'error');
                updateStatus('فشل في اختبار الرفع', 'error');
            }
        }

        async function testDownload() {
            log('📥 اختبار تحميل البيانات...', 'info');
            
            const result = await downloadProductsFromDrive();
            if (result) {
                log('✅ تم تحميل البيانات بنجاح', 'success');
                log(`📊 تم تحميل ${window.products ? window.products.length : 0} منتج`, 'info');
                updateStatus('تم اختبار التحميل بنجاح', 'success');
            } else {
                log('❌ فشل في تحميل البيانات', 'error');
                updateStatus('فشل في اختبار التحميل', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        }

        // Override UI update function for testing
        window.updateGoogleDriveUI = function(connected) {
            const connectBtn = document.getElementById('connectBtn');
            const uploadBtn = document.getElementById('uploadBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            if (connected) {
                connectBtn.innerHTML = '✅ متصل بـ Google';
                connectBtn.disabled = true;
                uploadBtn.disabled = false;
                log('🔗 تم الاتصال بـ Google Drive بنجاح', 'success');
                updateStatus('متصل بـ Google Drive', 'success');
            } else {
                connectBtn.innerHTML = '🔐 تسجيل دخول Google';
                connectBtn.disabled = false;
                uploadBtn.disabled = true;
                downloadBtn.disabled = true;
            }
        };

        // Initialize on load
        window.onload = function() {
            log('🌐 مرحباً بك في اختبار Google Drive', 'info');
            log('📋 اضغط "اختبار الاتصال" للبدء', 'info');
        };
    </script>
</body>
</html>
