@echo off
echo ========================================
echo    إنشاء ملف ZIP لرفعه على GitHub
echo    Create ZIP file for GitHub upload
echo ========================================
echo.

set "source_folder=%~dp0"
set "zip_name=diamond-eagles-inventory.zip"
set "temp_folder=%temp%\diamond-eagles-temp"

echo 📁 جاري إنشاء مجلد مؤقت...
echo 📁 Creating temporary folder...

REM Create temporary folder
if exist "%temp_folder%" rmdir /s /q "%temp_folder%"
mkdir "%temp_folder%"

echo 📋 جاري نسخ الملفات المطلوبة...
echo 📋 Copying required files...

REM Copy main files
copy "%source_folder%index.html" "%temp_folder%\" >nul
copy "%source_folder%script.js" "%temp_folder%\" >nul
copy "%source_folder%style.css" "%temp_folder%\" >nul
copy "%source_folder%google-drive-sync.js" "%temp_folder%\" >nul
copy "%source_folder%web-cloud-functions.js" "%temp_folder%\" >nul

REM Copy documentation
copy "%source_folder%*.md" "%temp_folder%\" >nul 2>&1

REM Copy assets if they exist
if exist "%source_folder%assets" (
    xcopy "%source_folder%assets" "%temp_folder%\assets" /e /i /q >nul 2>&1
)

if exist "%source_folder%images" (
    xcopy "%source_folder%images" "%temp_folder%\images" /e /i /q >nul 2>&1
)

echo 📝 جاري إنشاء ملف README...
echo 📝 Creating README file...

REM Create README.md for GitHub
echo # 🦅 تطبيق النسور الماسية للتجارة > "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 🎯 نظرة عامة >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo تطبيق ويب متكامل لإدارة المخزون والعملاء مع دعم المزامنة السحابية عبر Google Drive. >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## ✨ الميزات >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo - 📦 إدارة المنتجات والمخزون >> "%temp_folder%\README.md"
echo - 👥 إدارة العملاء والطلبات >> "%temp_folder%\README.md"
echo - 📊 تقارير وإحصائيات تفصيلية >> "%temp_folder%\README.md"
echo - 📄 تصدير التقارير (PDF, Word, Excel) >> "%temp_folder%\README.md"
echo - ☁️ مزامنة سحابية مع Google Drive >> "%temp_folder%\README.md"
echo - 📱 واجهة متجاوبة للهواتف والأجهزة اللوحية >> "%temp_folder%\README.md"
echo - 🔒 أمان وحماية البيانات >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 🚀 كيفية الاستخدام >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo 1. افتح ملف `index.html` في المتصفح >> "%temp_folder%\README.md"
echo 2. أو ارفع الملفات على خادم ويب >> "%temp_folder%\README.md"
echo 3. للمزامنة السحابية، اتبع دليل `GOOGLE_DRIVE_SETUP_GUIDE.md` >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 📋 متطلبات النظام >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo - متصفح ويب حديث (Chrome, Firefox, Safari, Edge) >> "%temp_folder%\README.md"
echo - اتصال بالإنترنت (للمزامنة السحابية) >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 🔧 الإعداد >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ### للاستخدام المحلي: >> "%temp_folder%\README.md"
echo ```bash >> "%temp_folder%\README.md"
echo # افتح index.html مباشرة في المتصفح >> "%temp_folder%\README.md"
echo # أو استخدم خادم محلي: >> "%temp_folder%\README.md"
echo python -m http.server 8000 >> "%temp_folder%\README.md"
echo ``` >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ### للمزامنة مع Google Drive: >> "%temp_folder%\README.md"
echo 1. اتبع دليل `GOOGLE_DRIVE_SETUP_GUIDE.md` >> "%temp_folder%\README.md"
echo 2. أنشئ مشروع في Google Cloud Console >> "%temp_folder%\README.md"
echo 3. فعّل Google Drive API >> "%temp_folder%\README.md"
echo 4. حديث ملف `google-drive-sync.js` بالبيانات الصحيحة >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 📱 النشر على الإنترنت >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo اتبع دليل `DEPLOY_TO_INTERNET_GUIDE.md` للنشر على: >> "%temp_folder%\README.md"
echo - Firebase Hosting >> "%temp_folder%\README.md"
echo - GitHub Pages >> "%temp_folder%\README.md"
echo - Netlify >> "%temp_folder%\README.md"
echo - Vercel >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 📞 الدعم >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo للمساعدة والدعم، راجع الملفات التوثيقية المرفقة. >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo ## 📄 الترخيص >> "%temp_folder%\README.md"
echo. >> "%temp_folder%\README.md"
echo هذا المشروع مطور خصيصاً لشركة النسور الماسية للتجارة. >> "%temp_folder%\README.md"

echo 🗜️ جاري ضغط الملفات...
echo 🗜️ Compressing files...

REM Create ZIP file using PowerShell
powershell -command "Compress-Archive -Path '%temp_folder%\*' -DestinationPath '%source_folder%%zip_name%' -Force"

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء ملف ZIP بنجاح!
    echo ✅ ZIP file created successfully!
    echo.
    echo 📁 اسم الملف: %zip_name%
    echo 📁 File name: %zip_name%
    echo 📍 المكان: %source_folder%
    echo 📍 Location: %source_folder%
    echo.
    echo 🌐 الخطوات التالية لرفعه على GitHub:
    echo 🌐 Next steps to upload to GitHub:
    echo.
    echo 1. اذهب إلى github.com وأنشئ حساب
    echo 1. Go to github.com and create account
    echo.
    echo 2. أنشئ Repository جديد:
    echo 2. Create new Repository:
    echo    - اسم Repository: diamond-eagles-inventory
    echo    - Name: diamond-eagles-inventory
    echo    - اختر Public
    echo    - Choose Public
    echo.
    echo 3. ارفع ملف ZIP:
    echo 3. Upload ZIP file:
    echo    - اضغط "uploading an existing file"
    echo    - Click "uploading an existing file"
    echo    - اسحب ملف %zip_name%
    echo    - Drag %zip_name%
    echo.
    echo 4. فعّل GitHub Pages:
    echo 4. Enable GitHub Pages:
    echo    - Settings ^> Pages
    echo    - Source: Deploy from branch
    echo    - Branch: main
    echo.
    echo 5. الرابط سيكون:
    echo 5. URL will be:
    echo    https://username.github.io/diamond-eagles-inventory
    echo.
) else (
    echo ❌ فشل في إنشاء ملف ZIP
    echo ❌ Failed to create ZIP file
)

REM Clean up temporary folder
if exist "%temp_folder%" rmdir /s /q "%temp_folder%"

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
