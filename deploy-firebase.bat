@echo off
echo ========================================
echo    نشر تطبيق النسور الماسية على Firebase
echo    Deploy Diamond Eagles to Firebase
echo ========================================
echo.

echo جاري التحقق من Firebase CLI...
echo Checking Firebase CLI...

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI غير مثبت
    echo ❌ Firebase CLI not installed
    echo.
    echo يرجى تثبيت Firebase CLI أولاً:
    echo Please install Firebase CLI first:
    echo npm install -g firebase-tools
    echo.
    pause
    exit /b 1
)

echo ✅ Firebase CLI مثبت
echo ✅ Firebase CLI installed
echo.

echo جاري التحقق من تسجيل الدخول...
echo Checking login status...

REM Check if user is logged in
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لم يتم تسجيل الدخول
    echo ❌ Not logged in
    echo.
    echo جاري تسجيل الدخول...
    echo Logging in...
    firebase login
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تسجيل الدخول
        echo ❌ Login failed
        pause
        exit /b 1
    )
)

echo ✅ تم تسجيل الدخول بنجاح
echo ✅ Successfully logged in
echo.

REM Check if firebase.json exists
if not exist "firebase.json" (
    echo 🔧 تهيئة مشروع Firebase...
    echo 🔧 Initializing Firebase project...
    echo.
    
    echo يرجى اتباع التعليمات:
    echo Please follow the instructions:
    echo 1. اختر "Hosting" فقط
    echo 1. Select "Hosting" only
    echo 2. اختر مشروع موجود أو أنشئ جديد
    echo 2. Select existing project or create new
    echo 3. Public directory: اكتب نقطة واحدة (.)
    echo 3. Public directory: type single dot (.)
    echo 4. Single-page app: اختر No
    echo 4. Single-page app: choose No
    echo 5. Overwrite index.html: اختر No
    echo 5. Overwrite index.html: choose No
    echo.
    
    firebase init hosting
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تهيئة المشروع
        echo ❌ Project initialization failed
        pause
        exit /b 1
    )
)

echo 🚀 جاري نشر التطبيق...
echo 🚀 Deploying application...
echo.

firebase deploy --only hosting

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ تم نشر التطبيق بنجاح!
    echo ✅ Application deployed successfully!
    echo ========================================
    echo.
    echo يمكنك الآن الوصول للتطبيق من أي مكان في العالم
    echo You can now access the app from anywhere in the world
    echo.
    echo 🔗 للحصول على الرابط:
    echo 🔗 To get the URL:
    echo firebase hosting:channel:list
    echo.
    echo 📱 لتحديث التطبيق مستقبلاً:
    echo 📱 To update the app in the future:
    echo firebase deploy
    echo.
) else (
    echo.
    echo ❌ فشل في نشر التطبيق
    echo ❌ Deployment failed
    echo.
    echo يرجى التحقق من:
    echo Please check:
    echo 1. اتصال الإنترنت
    echo 1. Internet connection
    echo 2. صحة ملفات المشروع
    echo 2. Project files integrity
    echo 3. أذونات Firebase
    echo 3. Firebase permissions
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
