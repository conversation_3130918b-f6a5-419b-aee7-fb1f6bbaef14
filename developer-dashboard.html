<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المطور - نظام إدارة المخزون</title>
    
    <!-- Fonts & Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="developer-dashboard.css">
</head>
<body>
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="dev-logo">
                    <i class="fas fa-code"></i>
                </div>
                <div class="header-text">
                    <h1>لوحة تحكم المطور</h1>
                    <p>إدارة شاملة لنظام إدارة المخزون</p>
                </div>
            </div>
            <div class="header-actions">
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator status-offline" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">غير متصل</span>
                </div>
                <button class="btn btn-primary" onclick="openMainApp()">
                    <i class="fas fa-external-link-alt"></i>
                    فتح التطبيق الرئيسي
                </button>
                <button class="btn btn-secondary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Quick Stats -->
            <section class="stats-section">
                <h2><i class="fas fa-chart-line"></i> إحصائيات سريعة</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalProducts">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCustomers">0</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="appVersion">2.0</h3>
                            <p>إصدار التطبيق</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="lastUpdate">اليوم</h3>
                            <p>آخر تحديث</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Control Panels -->
            <div class="panels-grid">
                <!-- Developer Info Panel -->
                <section class="control-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-user-cog"></i> معلومات المطور</h3>
                        <button class="btn btn-sm btn-outline" onclick="togglePanel('developerPanel')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="panel-content" id="developerPanel">
                        <form id="developerForm">
                            <div class="form-group">
                                <label for="developerName">اسم المطور</label>
                                <input type="text" id="developerName" value="كريم واصل" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="developerTitle">المسمى الوظيفي</label>
                                <input type="text" id="developerTitle" value="تطوير وبرمجة" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="developerDescription">الوصف</label>
                                <input type="text" id="developerDescription" value="مطور ذكي متطور" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="developerEmail">البريد الإلكتروني</label>
                                <input type="email" id="developerEmail" value="<EMAIL>" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="copyrightText">نص حقوق الطبع والنشر</label>
                                <textarea id="copyrightText" class="form-control" rows="3" placeholder="جميع الحقوق محفوظة لمطور التطبيق كريم واصل لصالح شركة النسور الماسية للتجارة">جميع الحقوق محفوظة لمطور التطبيق كريم واصل لصالح شركة النسور الماسية للتجارة</textarea>
                            </div>
                            <div class="form-group">
                                <label for="systemDescription">وصف النظام</label>
                                <input type="text" id="systemDescription" value="نظام إدارة مخزون بطاريات الدواجن" class="form-control">
                            </div>
                            <div class="text-preview-section">
                                <h4>معاينة النصوص</h4>
                                <div class="preview-box">
                                    <div class="preview-item">
                                        <label>وصف النظام:</label>
                                        <div class="preview-text" id="systemDescPreview">نظام إدارة مخزون بطاريات الدواجن</div>
                                    </div>
                                    <div class="preview-item">
                                        <label>حقوق الطبع والنشر:</label>
                                        <div class="preview-text" id="copyrightPreview">جميع الحقوق محفوظة لمطور التطبيق كريم واصل لصالح شركة النسور الماسية للتجارة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-primary" onclick="saveDeveloperInfo()">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetDeveloperInfo()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-info" onclick="previewTexts()">
                                    <i class="fas fa-eye"></i> معاينة النصوص
                                </button>
                            </div>
                        </form>
                    </div>
                </section>

                <!-- Splash Screen Panel -->
                <section class="control-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-desktop"></i> الشاشة الافتتاحية</h3>
                        <button class="btn btn-sm btn-outline" onclick="togglePanel('splashPanel')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="panel-content" id="splashPanel">
                        <div class="splash-controls">
                            <div class="control-group">
                                <h4>إعدادات العرض</h4>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="showSplashScreen" checked>
                                        <span class="checkmark"></span>
                                        عرض الشاشة الافتتاحية
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="splashDuration">مدة العرض (ثانية)</label>
                                    <input type="number" id="splashDuration" value="4" min="2" max="10" class="form-control">
                                </div>
                            </div>
                            <div class="control-group">
                                <h4>إجراءات سريعة</h4>
                                <div class="action-buttons">
                                    <button class="btn btn-info" onclick="previewSplashScreen()">
                                        <i class="fas fa-eye"></i> معاينة الشاشة
                                    </button>
                                    <button class="btn btn-warning" onclick="resetSplashScreen()">
                                        <i class="fas fa-refresh"></i> إعادة تعيين
                                    </button>
                                    <button class="btn btn-success" onclick="forceSplashScreen()">
                                        <i class="fas fa-play"></i> عرض فوري
                                    </button>
                                    <button class="btn btn-secondary" onclick="testConnection()">
                                        <i class="fas fa-wifi"></i> اختبار الاتصال
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- App Settings Panel -->
                <section class="control-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-sliders-h"></i> إعدادات التطبيق</h3>
                        <button class="btn btn-sm btn-outline" onclick="togglePanel('appSettingsPanel')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="panel-content" id="appSettingsPanel">
                        <div class="settings-grid">
                            <div class="setting-item">
                                <label>وضع التطوير</label>
                                <label class="switch">
                                    <input type="checkbox" id="devMode">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>عرض وحدة التحكم</label>
                                <label class="switch">
                                    <input type="checkbox" id="showConsole">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>التحديث التلقائي</label>
                                <label class="switch">
                                    <input type="checkbox" id="autoUpdate" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="saveAppSettings()">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Data Management Panel -->
                <section class="control-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
                        <button class="btn btn-sm btn-outline" onclick="togglePanel('dataPanel')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="panel-content" id="dataPanel">
                        <div class="data-actions">
                            <div class="action-group">
                                <h4>النسخ الاحتياطي</h4>
                                <button class="btn btn-success" onclick="exportAllData()">
                                    <i class="fas fa-download"></i> تصدير جميع البيانات
                                </button>
                                <button class="btn btn-info" onclick="importData()">
                                    <i class="fas fa-upload"></i> استيراد البيانات
                                </button>
                            </div>
                            <div class="action-group">
                                <h4>إعادة التعيين</h4>
                                <button class="btn btn-warning" onclick="clearCache()">
                                    <i class="fas fa-broom"></i> مسح التخزين المؤقت
                                </button>
                                <button class="btn btn-danger" onclick="resetAllData()">
                                    <i class="fas fa-trash-alt"></i> إعادة تعيين كاملة
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="dashboard-footer">
        <div class="footer-content">
            <p>&copy; 2024 لوحة تحكم المطور - نظام إدارة المخزون</p>
            <p>تطوير: <strong>كريم واصل</strong></p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="developer-dashboard.js"></script>
</body>
</html>
