// ===== DEVELOPER DASHBOARD JAVASCRIPT =====

// Configuration
const CONFIG = {
    mainAppUrl: './index.html',
    storageKeys: {
        developerInfo: 'developer_info',
        splashSettings: 'splash_settings',
        appSettings: 'app_settings'
    }
};

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل لوحة تحكم المطور');
    initializeDashboard();
});

// Initialize Dashboard Functions
function initializeDashboard() {
    loadDashboardData();
    loadDeveloperInfo();
    loadSplashSettings();
    loadAppSettings();
    updateStats();
    
    // Auto-refresh stats every 30 seconds
    setInterval(updateStats, 30000);
}

// Load Dashboard Data
function loadDashboardData() {
    try {
        // Load main app data if available
        const mainAppData = getMainAppData();
        if (mainAppData) {
            updateStatsFromMainApp(mainAppData);
        }
    } catch (error) {
        console.warn('⚠️ لا يمكن الوصول لبيانات التطبيق الرئيسي:', error);
    }
}

// Get Main App Data
function getMainAppData() {
    try {
        // Try to access main app localStorage
        const products = JSON.parse(localStorage.getItem('products') || '[]');
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
        
        return {
            products,
            customers,
            settings
        };
    } catch (error) {
        return null;
    }
}

// Update Stats from Main App
function updateStatsFromMainApp(data) {
    document.getElementById('totalProducts').textContent = data.products.length;
    document.getElementById('totalCustomers').textContent = data.customers.length;
    
    // Update last update time
    const lastUpdate = new Date().toLocaleDateString('ar-SA');
    document.getElementById('lastUpdate').textContent = lastUpdate;
}

// Update Stats
function updateStats() {
    const mainAppData = getMainAppData();
    if (mainAppData) {
        updateStatsFromMainApp(mainAppData);
    }
    
    // Update app version
    document.getElementById('appVersion').textContent = '2.0';
}

// Toggle Panel
function togglePanel(panelId) {
    const panel = document.getElementById(panelId);
    const button = panel.previousElementSibling.querySelector('.btn-outline i');
    
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        button.className = 'fas fa-chevron-up';
    } else {
        panel.style.display = 'none';
        button.className = 'fas fa-chevron-down';
    }
}

// Developer Info Functions
function loadDeveloperInfo() {
    const saved = localStorage.getItem(CONFIG.storageKeys.developerInfo);
    if (saved) {
        const info = JSON.parse(saved);
        document.getElementById('developerName').value = info.name || 'كريم واصل';
        document.getElementById('developerTitle').value = info.title || 'تطوير وبرمجة';
        document.getElementById('developerDescription').value = info.description || 'مطور ذكي متطور';
        document.getElementById('developerEmail').value = info.email || '<EMAIL>';
        document.getElementById('copyrightText').value = info.copyrightText || 'جميع الحقوق محفوظة لمطور التطبيق كريم واصل لصالح شركة النسور الماسية للتجارة';
        document.getElementById('systemDescription').value = info.systemDescription || 'نظام إدارة مخزون بطاريات الدواجن';
    }

    // Update preview
    updateTextPreview();

    // Setup real-time preview
    setupTextPreviewListeners();
}

function saveDeveloperInfo() {
    const info = {
        name: document.getElementById('developerName').value,
        title: document.getElementById('developerTitle').value,
        description: document.getElementById('developerDescription').value,
        email: document.getElementById('developerEmail').value,
        lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem(CONFIG.storageKeys.developerInfo, JSON.stringify(info));
    
    // Update main app if possible
    updateMainAppDeveloperInfo(info);
    
    showNotification('تم حفظ معلومات المطور بنجاح', 'success');
}

function resetDeveloperInfo() {
    document.getElementById('developerName').value = 'كريم واصل';
    document.getElementById('developerTitle').value = 'تطوير وبرمجة';
    document.getElementById('developerDescription').value = 'مطور ذكي متطور';
    document.getElementById('developerEmail').value = '<EMAIL>';
    document.getElementById('copyrightText').value = 'جميع الحقوق محفوظة لمطور التطبيق كريم واصل لصالح شركة النسور الماسية للتجارة';
    document.getElementById('systemDescription').value = 'نظام إدارة مخزون بطاريات الدواجن';

    showNotification('تم إعادة تعيين معلومات المطور', 'info');
}

// Update Main App Developer Info
function updateMainAppDeveloperInfo(info) {
    try {
        // Update main app settings
        const mainSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
        mainSettings.developer = info;
        localStorage.setItem('systemSettings', JSON.stringify(mainSettings));

        // Mark settings as updated to trigger splash screen
        localStorage.setItem('settings_updated', 'true');

        // Trigger storage event for cross-window communication
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'systemSettings',
            newValue: JSON.stringify(mainSettings),
            url: window.location.href
        }));

        // Also trigger developer info update event
        localStorage.setItem('developer_info_updated', Date.now().toString());

        console.log('✅ تم تحديث معلومات المطور في التطبيق الرئيسي');

        // Try to communicate with main app window if available
        broadcastToMainApp('developerInfoUpdated', info);

    } catch (error) {
        console.warn('⚠️ لا يمكن تحديث التطبيق الرئيسي:', error);
    }
}

// Splash Screen Functions
function loadSplashSettings() {
    const saved = localStorage.getItem(CONFIG.storageKeys.splashSettings);
    if (saved) {
        const settings = JSON.parse(saved);
        document.getElementById('showSplashScreen').checked = settings.enabled !== false;
        document.getElementById('splashDuration').value = settings.duration || 4;
    }
}

function saveSplashSettings() {
    const settings = {
        enabled: document.getElementById('showSplashScreen').checked,
        duration: parseInt(document.getElementById('splashDuration').value),
        lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem(CONFIG.storageKeys.splashSettings, JSON.stringify(settings));
    showNotification('تم حفظ إعدادات الشاشة الافتتاحية', 'success');
}

function previewSplashScreen() {
    // Open main app in new tab and force splash screen
    const newWindow = window.open(CONFIG.mainAppUrl, '_blank');
    
    // Try to trigger splash screen after page loads
    setTimeout(() => {
        try {
            newWindow.localStorage.removeItem('app_entered');
            newWindow.location.reload();
        } catch (error) {
            console.warn('لا يمكن التحكم في النافذة الجديدة');
        }
    }, 1000);
    
    showNotification('تم فتح معاينة الشاشة الافتتاحية', 'info');
}

function resetSplashScreen() {
    localStorage.removeItem('app_entered');
    localStorage.setItem('settings_updated', 'true');
    showNotification('تم إعادة تعيين الشاشة الافتتاحية', 'success');
}

function forceSplashScreen() {
    // Reset splash screen state
    localStorage.removeItem('app_entered');
    localStorage.setItem('settings_updated', 'true');
    
    // Open main app
    window.open(CONFIG.mainAppUrl, '_blank');
    
    showNotification('تم فتح التطبيق مع الشاشة الافتتاحية', 'success');
}

// App Settings Functions
function loadAppSettings() {
    const saved = localStorage.getItem(CONFIG.storageKeys.appSettings);
    if (saved) {
        const settings = JSON.parse(saved);
        document.getElementById('devMode').checked = settings.devMode || false;
        document.getElementById('showConsole').checked = settings.showConsole || false;
        document.getElementById('autoUpdate').checked = settings.autoUpdate !== false;
    }
}

function saveAppSettings() {
    const settings = {
        devMode: document.getElementById('devMode').checked,
        showConsole: document.getElementById('showConsole').checked,
        autoUpdate: document.getElementById('autoUpdate').checked,
        lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem(CONFIG.storageKeys.appSettings, JSON.stringify(settings));
    
    // Apply settings immediately
    applyAppSettings(settings);
    
    showNotification('تم حفظ إعدادات التطبيق', 'success');
}

function applyAppSettings(settings) {
    // Apply dev mode
    if (settings.devMode) {
        document.body.classList.add('dev-mode');
    } else {
        document.body.classList.remove('dev-mode');
    }
    
    // Show/hide console
    if (settings.showConsole) {
        console.log('🔧 وضع المطور مفعل');
    }
}

// Data Management Functions
function exportAllData() {
    try {
        const allData = {
            products: JSON.parse(localStorage.getItem('products') || '[]'),
            customers: JSON.parse(localStorage.getItem('customers') || '[]'),
            settings: JSON.parse(localStorage.getItem('systemSettings') || '{}'),
            developer: JSON.parse(localStorage.getItem(CONFIG.storageKeys.developerInfo) || '{}'),
            exportDate: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(allData, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        showNotification('تم تصدير جميع البيانات بنجاح', 'success');
    } catch (error) {
        showNotification('خطأ في تصدير البيانات', 'error');
        console.error(error);
    }
}

function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                
                // Confirm import
                if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                    if (data.products) localStorage.setItem('products', JSON.stringify(data.products));
                    if (data.customers) localStorage.setItem('customers', JSON.stringify(data.customers));
                    if (data.settings) localStorage.setItem('systemSettings', JSON.stringify(data.settings));
                    if (data.developer) localStorage.setItem(CONFIG.storageKeys.developerInfo, JSON.stringify(data.developer));
                    
                    showNotification('تم استيراد البيانات بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                }
            } catch (error) {
                showNotification('خطأ في قراءة ملف البيانات', 'error');
                console.error(error);
            }
        };
        reader.readAsText(file);
    };
    
    input.click();
}

function clearCache() {
    if (confirm('هل أنت متأكد من مسح التخزين المؤقت؟')) {
        localStorage.removeItem('app_entered');
        localStorage.removeItem('settings_updated');
        
        showNotification('تم مسح التخزين المؤقت', 'success');
    }
}

function resetAllData() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع البيانات نهائياً!')) {
            localStorage.clear();
            showNotification('تم إعادة تعيين جميع البيانات', 'warning');
            setTimeout(() => location.reload(), 1000);
        }
    }
}

// Utility Functions
function openMainApp() {
    window.open(CONFIG.mainAppUrl, '_blank');
}

function refreshDashboard() {
    location.reload();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    // Style notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: getNotificationColor(type),
        color: 'white',
        padding: '15px 20px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        zIndex: '10000',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        minWidth: '300px',
        animation: 'slideInRight 0.3s ease'
    });
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Advanced Developer Functions
function injectCustomCode() {
    const code = prompt('أدخل الكود المخصص (JavaScript):');
    if (code) {
        try {
            eval(code);
            showNotification('تم تنفيذ الكود بنجاح', 'success');
        } catch (error) {
            showNotification('خطأ في تنفيذ الكود: ' + error.message, 'error');
        }
    }
}

function generateReport() {
    const data = getMainAppData();
    if (!data) {
        showNotification('لا توجد بيانات متاحة', 'warning');
        return;
    }

    const report = {
        generatedAt: new Date().toISOString(),
        summary: {
            totalProducts: data.products.length,
            totalCustomers: data.customers.length,
            activeProducts: data.products.filter(p => p.status === 'متاح').length,
            regularCustomers: data.customers.filter(c => c.type === 'عادي').length
        },
        details: {
            products: data.products,
            customers: data.customers,
            settings: data.settings
        }
    };

    const reportStr = JSON.stringify(report, null, 2);
    const blob = new Blob([reportStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.json`;
    a.click();

    URL.revokeObjectURL(url);
    showNotification('تم إنشاء التقرير الشامل', 'success');
}

function monitorPerformance() {
    const startTime = performance.now();

    // Monitor main app performance
    setTimeout(() => {
        const endTime = performance.now();
        const loadTime = endTime - startTime;

        console.log(`⚡ وقت التحميل: ${loadTime.toFixed(2)} مللي ثانية`);

        // Check memory usage if available
        if (performance.memory) {
            console.log(`💾 استخدام الذاكرة: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
        }

        showNotification(`تم فحص الأداء - وقت التحميل: ${loadTime.toFixed(2)}ms`, 'info');
    }, 100);
}

function debugMainApp() {
    const debugInfo = {
        localStorage: Object.keys(localStorage),
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
        language: navigator.language,
        cookiesEnabled: navigator.cookieEnabled,
        onlineStatus: navigator.onLine
    };

    console.log('🔍 معلومات التشخيص:', debugInfo);
    showNotification('تم عرض معلومات التشخيص في وحدة التحكم', 'info');
}

// Real-time monitoring
function startRealTimeMonitoring() {
    setInterval(() => {
        const data = getMainAppData();
        if (data) {
            // Update real-time stats
            updateStatsFromMainApp(data);

            // Log activity
            console.log(`📊 مراقبة مباشرة - المنتجات: ${data.products.length}, العملاء: ${data.customers.length}`);
        }
    }, 5000); // Every 5 seconds
}

// Initialize real-time monitoring
startRealTimeMonitoring();

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + R: Refresh dashboard
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }

    // Ctrl + O: Open main app
    if (e.ctrlKey && e.key === 'o') {
        e.preventDefault();
        openMainApp();
    }

    // Ctrl + S: Save all settings
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveDeveloperInfo();
        saveSplashSettings();
        saveAppSettings();
        showNotification('تم حفظ جميع الإعدادات', 'success');
    }
});

// Add developer tools menu
function createDeveloperMenu() {
    const menu = document.createElement('div');
    menu.id = 'developerMenu';
    menu.innerHTML = `
        <div class="dev-menu-header">
            <h4>أدوات المطور المتقدمة</h4>
            <button onclick="toggleDeveloperMenu()" class="btn btn-sm btn-outline">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="dev-menu-content">
            <button onclick="injectCustomCode()" class="btn btn-sm btn-info">
                <i class="fas fa-code"></i> تنفيذ كود مخصص
            </button>
            <button onclick="generateReport()" class="btn btn-sm btn-success">
                <i class="fas fa-chart-bar"></i> إنشاء تقرير شامل
            </button>
            <button onclick="monitorPerformance()" class="btn btn-sm btn-warning">
                <i class="fas fa-tachometer-alt"></i> فحص الأداء
            </button>
            <button onclick="debugMainApp()" class="btn btn-sm btn-secondary">
                <i class="fas fa-bug"></i> تشخيص التطبيق
            </button>
        </div>
    `;

    // Style the menu
    Object.assign(menu.style, {
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '10px',
        padding: '20px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
        zIndex: '10001',
        minWidth: '300px',
        display: 'none'
    });

    document.body.appendChild(menu);
}

function toggleDeveloperMenu() {
    const menu = document.getElementById('developerMenu');
    if (!menu) {
        createDeveloperMenu();
        document.getElementById('developerMenu').style.display = 'block';
    } else {
        menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
    }
}

// Add developer menu button to header
function addDeveloperMenuButton() {
    const headerActions = document.querySelector('.header-actions');
    const devButton = document.createElement('button');
    devButton.className = 'btn btn-info';
    devButton.innerHTML = '<i class="fas fa-tools"></i> أدوات متقدمة';
    devButton.onclick = toggleDeveloperMenu;

    headerActions.appendChild(devButton);
}

// Initialize developer menu
setTimeout(addDeveloperMenuButton, 1000);

// Text Preview Functions
function updateTextPreview() {
    const systemDesc = document.getElementById('systemDescription').value;
    const copyrightText = document.getElementById('copyrightText').value;

    document.getElementById('systemDescPreview').textContent = systemDesc;
    document.getElementById('copyrightPreview').textContent = copyrightText;
}

function setupTextPreviewListeners() {
    const systemDescInput = document.getElementById('systemDescription');
    const copyrightInput = document.getElementById('copyrightText');

    if (systemDescInput) {
        systemDescInput.addEventListener('input', updateTextPreview);
    }

    if (copyrightInput) {
        copyrightInput.addEventListener('input', updateTextPreview);
    }
}

function previewTexts() {
    const systemDesc = document.getElementById('systemDescription').value;
    const copyrightText = document.getElementById('copyrightText').value;

    const previewWindow = window.open('', '_blank', 'width=600,height=400');
    previewWindow.document.write(`
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة النصوص</title>
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    padding: 20px;
                    background: #f8f9fa;
                    line-height: 1.6;
                }
                .preview-container {
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    max-width: 500px;
                    margin: 0 auto;
                }
                .preview-title {
                    color: #667eea;
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin-bottom: 20px;
                    text-align: center;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 10px;
                }
                .preview-section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    border-right: 4px solid #667eea;
                }
                .preview-label {
                    font-weight: bold;
                    color: #495057;
                    margin-bottom: 10px;
                    font-size: 1.1rem;
                }
                .preview-content {
                    color: #212529;
                    font-size: 1rem;
                    line-height: 1.5;
                }
                .close-btn {
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-family: inherit;
                    margin-top: 20px;
                    width: 100%;
                }
                .close-btn:hover {
                    background: #5a6fd8;
                }
            </style>
        </head>
        <body>
            <div class="preview-container">
                <div class="preview-title">معاينة النصوص المخصصة</div>

                <div class="preview-section">
                    <div class="preview-label">وصف النظام:</div>
                    <div class="preview-content">${systemDesc}</div>
                </div>

                <div class="preview-section">
                    <div class="preview-label">نص حقوق الطبع والنشر:</div>
                    <div class="preview-content">${copyrightText}</div>
                </div>

                <button class="close-btn" onclick="window.close()">إغلاق المعاينة</button>
            </div>
        </body>
        </html>
    `);
    previewWindow.document.close();

    showNotification('تم فتح نافذة معاينة النصوص', 'info');
}

// Cross-Window Communication System
let mainAppWindow = null;
const broadcastChannel = new BroadcastChannel('developer-dashboard');

// Broadcast message to main app
function broadcastToMainApp(type, data) {
    const message = {
        type: type,
        data: data,
        timestamp: Date.now(),
        source: 'developer-dashboard'
    };

    // Use BroadcastChannel for modern browsers
    try {
        broadcastChannel.postMessage(message);
        console.log('📡 تم إرسال رسالة للتطبيق الرئيسي:', type);
    } catch (error) {
        console.warn('⚠️ فشل في إرسال الرسالة:', error);
    }

    // Fallback: Use localStorage events
    localStorage.setItem('dashboard_message', JSON.stringify(message));
    localStorage.removeItem('dashboard_message');
}

// Listen for messages from main app
broadcastChannel.addEventListener('message', function(event) {
    const message = event.data;
    console.log('📨 تم استلام رسالة من التطبيق الرئيسي:', message);

    // Update connection status
    updateConnectionStatus(true);

    switch(message.type) {
        case 'mainAppLoaded':
            showNotification('تم الاتصال بالتطبيق الرئيسي', 'success');
            updateStats();
            break;
        case 'dataUpdated':
            updateStats();
            showNotification('تم تحديث البيانات من التطبيق الرئيسي', 'info');
            break;
        case 'settingsUpdated':
            loadDeveloperInfo();
            showNotification('تم تحديث الإعدادات من التطبيق الرئيسي', 'info');
            break;
    }
});

// Update connection status
function updateConnectionStatus(isConnected) {
    const indicator = document.getElementById('statusIndicator');
    const text = document.getElementById('statusText');

    if (isConnected) {
        indicator.className = 'status-indicator status-online';
        text.textContent = 'متصل';
        text.style.color = '#28a745';
    } else {
        indicator.className = 'status-indicator status-offline';
        text.textContent = 'غير متصل';
        text.style.color = '#dc3545';
    }

    // Store last connection time
    localStorage.setItem('last_connection_check', Date.now().toString());
}

// Check connection periodically
function checkConnection() {
    const lastCheck = localStorage.getItem('last_connection_check');
    const currentTime = Date.now();

    // If no recent activity, assume disconnected
    if (!lastCheck || (currentTime - parseInt(lastCheck)) > 10000) {
        updateConnectionStatus(false);
    }
}

// Start connection monitoring
setInterval(checkConnection, 5000);

// Test connection with main app
function testConnection() {
    showNotification('جاري اختبار الاتصال...', 'info');

    // Send ping message
    broadcastToMainApp('ping', {
        timestamp: Date.now(),
        source: 'dashboard-test'
    });

    // Wait for response
    let responseReceived = false;

    const responseListener = function(event) {
        if (event.data.type === 'pong') {
            responseReceived = true;
            updateConnectionStatus(true);
            showNotification('✅ الاتصال مع التطبيق الرئيسي يعمل بشكل صحيح', 'success');
            broadcastChannel.removeEventListener('message', responseListener);
        }
    };

    broadcastChannel.addEventListener('message', responseListener);

    // Timeout after 3 seconds
    setTimeout(() => {
        if (!responseReceived) {
            updateConnectionStatus(false);
            showNotification('❌ لا يمكن الاتصال بالتطبيق الرئيسي', 'error');
            broadcastChannel.removeEventListener('message', responseListener);
        }
    }, 3000);
}

// Enhanced save functions with broadcasting
function saveDeveloperInfoEnhanced() {
    const info = {
        name: document.getElementById('developerName').value,
        title: document.getElementById('developerTitle').value,
        description: document.getElementById('developerDescription').value,
        email: document.getElementById('developerEmail').value,
        copyrightText: document.getElementById('copyrightText').value,
        systemDescription: document.getElementById('systemDescription').value,
        lastUpdated: new Date().toISOString()
    };

    // Save locally
    localStorage.setItem(CONFIG.storageKeys.developerInfo, JSON.stringify(info));

    // Update main app
    updateMainAppDeveloperInfo(info);

    // Force refresh main app splash screen
    forceSplashScreenRefresh();

    // Wait a moment then check if main app received the update
    setTimeout(() => {
        checkMainAppSync();
    }, 1000);

    showNotification('تم حفظ معلومات المطور وتحديث التطبيق الرئيسي', 'success');
}

// Check if main app is synced
function checkMainAppSync() {
    const mainAppUpdated = localStorage.getItem('main_app_updated');
    const currentTime = Date.now();

    if (mainAppUpdated && (currentTime - parseInt(mainAppUpdated)) < 5000) {
        showNotification('✅ تم تأكيد التحديث في التطبيق الرئيسي', 'success');
    } else {
        showNotification('⚠️ قد لا يكون التطبيق الرئيسي مفتوحاً', 'warning');
    }
}

// Force splash screen refresh in main app
function forceSplashScreenRefresh() {
    // Clear app entered flag to force splash screen
    localStorage.removeItem('app_entered');
    localStorage.setItem('settings_updated', 'true');
    localStorage.setItem('force_splash_refresh', Date.now().toString());

    // Broadcast to main app
    broadcastToMainApp('forceSplashRefresh', {
        timestamp: Date.now()
    });
}

// Enhanced splash settings save
function saveSplashSettingsEnhanced() {
    const settings = {
        enabled: document.getElementById('showSplashScreen').checked,
        duration: parseInt(document.getElementById('splashDuration').value),
        lastUpdated: new Date().toISOString()
    };

    localStorage.setItem(CONFIG.storageKeys.splashSettings, JSON.stringify(settings));

    // Update main app settings
    const mainSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
    mainSettings.splash = settings;
    localStorage.setItem('systemSettings', JSON.stringify(mainSettings));

    // Force splash screen refresh
    forceSplashScreenRefresh();

    // Broadcast changes
    broadcastToMainApp('splashSettingsUpdated', settings);

    showNotification('تم حفظ إعدادات الشاشة الافتتاحية', 'success');
}

// Real-time sync with main app
function startRealTimeSync() {
    // Listen for localStorage changes
    window.addEventListener('storage', function(e) {
        if (e.key === 'systemSettings' || e.key === 'products' || e.key === 'customers') {
            console.log('🔄 تم اكتشاف تغيير في البيانات:', e.key);
            updateStats();
        }
    });

    // Periodic sync check
    setInterval(() => {
        syncWithMainApp();
    }, 2000); // Every 2 seconds
}

// Sync with main app
function syncWithMainApp() {
    const lastSync = localStorage.getItem('last_sync_check');
    const currentTime = Date.now();

    // Check if main app has been updated
    const mainAppUpdated = localStorage.getItem('main_app_updated');
    if (mainAppUpdated && mainAppUpdated !== lastSync) {
        console.log('🔄 تم اكتشاف تحديث في التطبيق الرئيسي');
        updateStats();
        localStorage.setItem('last_sync_check', currentTime.toString());
    }
}

// Override original save functions
function saveDeveloperInfo() {
    saveDeveloperInfoEnhanced();
}

function saveSplashSettings() {
    saveSplashSettingsEnhanced();
}

// Start real-time sync
startRealTimeSync();
