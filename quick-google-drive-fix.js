// Quick Google Drive Fix for Diamond Eagles
// حل سريع لـ Google Drive للنسور الماسية

console.log('🔧 تحميل الحل السريع لـ Google Drive...');

// تحديث حالة الاتصال فوراً
function updateConnectionStatusQuickly() {
    const statusElement = document.getElementById('googleDriveStatus');
    const userElement = document.getElementById('googleDriveUser');
    const connectBtn = document.getElementById('googleDriveConnectBtn');
    
    if (statusElement) {
        const statusText = statusElement.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = 'جاري التحقق...';
            statusText.className = 'status-text offline';
        }
    }
    
    if (userElement) {
        userElement.textContent = 'جاري التحقق...';
    }
    
    if (connectBtn) {
        connectBtn.textContent = 'جاري التحميل...';
        connectBtn.disabled = true;
    }
}

// محاولة الاتصال بـ Google API
async function attemptGoogleConnection() {
    try {
        console.log('🔄 محاولة الاتصال بـ Google API...');
        
        // تحميل Google API
        if (typeof gapi === 'undefined') {
            await loadGoogleAPIScript();
        }
        
        // تهيئة gapi
        await new Promise((resolve) => {
            gapi.load('auth2', resolve);
        });
        
        await new Promise((resolve) => {
            gapi.load('client', resolve);
        });
        
        // تهيئة العميل
        await gapi.client.init({
            discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest']
        });
        
        // تهيئة المصادقة
        const authInstance = await gapi.auth2.init({
            client_id: getClientId(),
            scope: 'https://www.googleapis.com/auth/drive.file'
        });
        
        console.log('✅ تم تحميل Google API بنجاح');
        
        // تحديث الواجهة
        updateUIAfterLoad(authInstance);
        
    } catch (error) {
        console.error('❌ خطأ في تحميل Google API:', error);
        showConnectionError();
    }
}

// تحميل Google API Script
function loadGoogleAPIScript() {
    return new Promise((resolve, reject) => {
        if (typeof gapi !== 'undefined') {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.onload = resolve;
        script.onerror = () => {
            console.error('فشل في تحميل Google API');
            reject(new Error('فشل في تحميل Google API'));
        };
        document.head.appendChild(script);
    });
}

// الحصول على Client ID
function getClientId() {
    // جرب من الملفات المختلفة
    if (typeof GOOGLE_DRIVE_CONFIG !== 'undefined' && GOOGLE_DRIVE_CONFIG.CLIENT_ID) {
        return GOOGLE_DRIVE_CONFIG.CLIENT_ID;
    }
    
    if (typeof SIMPLE_DRIVE_CONFIG !== 'undefined' && SIMPLE_DRIVE_CONFIG.CLIENT_ID) {
        return SIMPLE_DRIVE_CONFIG.CLIENT_ID;
    }
    
    // Client ID افتراضي للاختبار
    return '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com';
}

// تحديث الواجهة بعد التحميل
function updateUIAfterLoad(authInstance) {
    const statusElement = document.getElementById('googleDriveStatus');
    const userElement = document.getElementById('googleDriveUser');
    const connectBtn = document.getElementById('googleDriveConnectBtn');
    const uploadBtn = document.getElementById('googleDriveUploadBtn');
    const downloadBtn = document.getElementById('googleDriveDownloadBtn');
    
    // تحقق من حالة تسجيل الدخول
    const isSignedIn = authInstance.isSignedIn.get();
    
    if (statusElement) {
        const statusText = statusElement.querySelector('.status-text');
        if (statusText) {
            if (isSignedIn) {
                statusText.textContent = 'متصل';
                statusText.className = 'status-text connected';
            } else {
                statusText.textContent = 'جاهز للاتصال';
                statusText.className = 'status-text offline';
            }
        }
    }
    
    if (userElement) {
        if (isSignedIn) {
            const user = authInstance.currentUser.get();
            userElement.textContent = user.getBasicProfile().getName();
        } else {
            userElement.textContent = 'غير متصل';
        }
    }
    
    if (connectBtn) {
        connectBtn.disabled = false;
        if (isSignedIn) {
            connectBtn.textContent = 'تسجيل خروج';
            connectBtn.onclick = signOutQuick;
        } else {
            connectBtn.textContent = 'تسجيل دخول Google';
            connectBtn.onclick = signInQuick;
        }
    }
    
    if (uploadBtn) {
        uploadBtn.disabled = !isSignedIn;
    }
    
    if (downloadBtn) {
        downloadBtn.disabled = !isSignedIn;
    }
}

// تسجيل دخول سريع
async function signInQuick() {
    try {
        console.log('🔐 تسجيل دخول سريع...');
        
        const authInstance = gapi.auth2.getAuthInstance();
        const user = await authInstance.signIn();
        
        console.log('✅ تم تسجيل الدخول بنجاح');
        updateUIAfterLoad(authInstance);
        
        // إشعار نجاح
        if (typeof showSyncNotification === 'function') {
            showSyncNotification('تم تسجيل الدخول بنجاح', 'success');
        } else {
            alert('تم تسجيل الدخول بنجاح');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الدخول:', error);
        
        if (typeof showSyncNotification === 'function') {
            showSyncNotification('فشل في تسجيل الدخول', 'error');
        } else {
            alert('فشل في تسجيل الدخول: ' + error.message);
        }
    }
}

// تسجيل خروج سريع
async function signOutQuick() {
    try {
        const authInstance = gapi.auth2.getAuthInstance();
        await authInstance.signOut();
        
        console.log('✅ تم تسجيل الخروج');
        updateUIAfterLoad(authInstance);
        
        if (typeof showSyncNotification === 'function') {
            showSyncNotification('تم تسجيل الخروج', 'info');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الخروج:', error);
    }
}

// عرض خطأ الاتصال
function showConnectionError() {
    const statusElement = document.getElementById('googleDriveStatus');
    const userElement = document.getElementById('googleDriveUser');
    const connectBtn = document.getElementById('googleDriveConnectBtn');
    
    if (statusElement) {
        const statusText = statusElement.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = 'خطأ في الاتصال';
            statusText.className = 'status-text offline';
        }
    }
    
    if (userElement) {
        userElement.textContent = 'خطأ في التحميل';
    }
    
    if (connectBtn) {
        connectBtn.textContent = 'إعادة المحاولة';
        connectBtn.disabled = false;
        connectBtn.onclick = () => {
            updateConnectionStatusQuickly();
            attemptGoogleConnection();
        };
    }
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء الحل السريع لـ Google Drive...');
    
    // تحديث الحالة فوراً
    updateConnectionStatusQuickly();
    
    // محاولة الاتصال بعد تأخير قصير
    setTimeout(() => {
        attemptGoogleConnection();
    }, 2000);
});

// تصدير الوظائف للاستخدام العام
window.signInQuick = signInQuick;
window.signOutQuick = signOutQuick;
window.attemptGoogleConnection = attemptGoogleConnection;

console.log('✅ تم تحميل الحل السريع لـ Google Drive');
