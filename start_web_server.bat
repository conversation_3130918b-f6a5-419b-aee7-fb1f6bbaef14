@echo off
echo ========================================
echo    تشغيل تطبيق النسور الماسية السحابي
echo    Diamond Eagles Cloud Web Server
echo ========================================
echo.

echo جاري بدء الخادم المحلي...
echo Starting local web server...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo تم العثور على Python - استخدام خادم Python
    echo Python found - Using Python server
    echo.
    echo الخادم يعمل على: http://localhost:8000
    echo Server running on: http://localhost:8000
    echo.
    echo اضغط Ctrl+C لإيقاف الخادم
    echo Press Ctrl+C to stop the server
    echo.
    
    REM Start Python HTTP server
    python -m http.server 8000
    goto :end
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo تم العثور على Node.js - استخدام خادم Node.js
    echo Node.js found - Using Node.js server
    echo.
    
    REM Create simple Node.js server
    echo const http = require('http'); > temp_server.js
    echo const fs = require('fs'); >> temp_server.js
    echo const path = require('path'); >> temp_server.js
    echo const server = http.createServer((req, res) =^> { >> temp_server.js
    echo   let filePath = '.' + req.url; >> temp_server.js
    echo   if (filePath === './') filePath = './index.html'; >> temp_server.js
    echo   const extname = path.extname(filePath); >> temp_server.js
    echo   let contentType = 'text/html'; >> temp_server.js
    echo   if (extname === '.css') contentType = 'text/css'; >> temp_server.js
    echo   if (extname === '.js') contentType = 'text/javascript'; >> temp_server.js
    echo   fs.readFile(filePath, (err, content) =^> { >> temp_server.js
    echo     if (err) { res.writeHead(404); res.end('File not found'); return; } >> temp_server.js
    echo     res.writeHead(200, { 'Content-Type': contentType }); >> temp_server.js
    echo     res.end(content, 'utf-8'); >> temp_server.js
    echo   }); >> temp_server.js
    echo }); >> temp_server.js
    echo server.listen(8000, () =^> console.log('Server running on http://localhost:8000')); >> temp_server.js
    
    echo الخادم يعمل على: http://localhost:8000
    echo Server running on: http://localhost:8000
    echo.
    
    node temp_server.js
    del temp_server.js
    goto :end
)

REM If neither Python nor Node.js is available
echo لم يتم العثور على Python أو Node.js
echo Python or Node.js not found
echo.
echo سيتم فتح الملف مباشرة في المتصفح
echo Opening file directly in browser
echo.

start index.html

:end
echo.
echo تم إيقاف الخادم
echo Server stopped
pause
