<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(39, 174, 96, 0.8); }
        .error { background: rgba(231, 76, 60, 0.8); }
        .warning { background: rgba(243, 156, 18, 0.8); }
        .info { background: rgba(52, 152, 219, 0.8); }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        #log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            text-align: left;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار Firebase - النسور الماسية</h1>
        
        <div id="status" class="status info">
            جاري التحقق من Firebase...
        </div>
        
        <div>
            <button onclick="testConnection()">🔍 اختبار الاتصال</button>
            <button onclick="testAuth()">🔐 اختبار المصادقة</button>
            <button onclick="testFirestore()">📊 اختبار قاعدة البيانات</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, getDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration - نفس التكوين من firebase-config.js
        const firebaseConfig = {
            // 👇 الصق نفس التكوين من firebase-config.js هنا
            apiKey: "YOUR_API_KEY_HERE",
            authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
            projectId: "YOUR_PROJECT_ID", 
            storageBucket: "YOUR_PROJECT_ID.appspot.com",
            messagingSenderId: "YOUR_SENDER_ID",
            appId: "YOUR_APP_ID"
            // 👆 استبدل بالتكوين الحقيقي
        };

        let app, db, auth;
        let currentUser = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const color = type === 'error' ? '#e74c3c' : 
                         type === 'success' ? '#27ae60' : 
                         type === 'warning' ? '#f39c12' : '#3498db';
            
            logDiv.innerHTML += `<div style="color: ${color}; margin: 5px 0;">
                [${time}] ${message}
            </div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Initialize Firebase
        try {
            app = initializeApp(firebaseConfig);
            db = getFirestore(app);
            auth = getAuth(app);
            
            log('✅ تم تهيئة Firebase بنجاح', 'success');
            updateStatus('Firebase جاهز للاختبار', 'success');
        } catch (error) {
            log('❌ فشل في تهيئة Firebase: ' + error.message, 'error');
            updateStatus('خطأ في تكوين Firebase', 'error');
        }

        // Test functions
        window.testConnection = function() {
            log('🔍 اختبار الاتصال بـ Firebase...', 'info');
            
            if (app) {
                log('✅ الاتصال بـ Firebase يعمل', 'success');
                updateStatus('متصل بـ Firebase', 'success');
            } else {
                log('❌ لا يوجد اتصال بـ Firebase', 'error');
                updateStatus('غير متصل بـ Firebase', 'error');
            }
        };

        window.testAuth = async function() {
            log('🔐 اختبار المصادقة المجهولة...', 'info');
            
            try {
                const userCredential = await signInAnonymously(auth);
                currentUser = userCredential.user;
                
                log('✅ تم تسجيل الدخول بنجاح', 'success');
                log('👤 معرف المستخدم: ' + currentUser.uid.substring(0, 8) + '...', 'info');
                updateStatus('تم تسجيل الدخول بنجاح', 'success');
                
            } catch (error) {
                log('❌ فشل في تسجيل الدخول: ' + error.message, 'error');
                updateStatus('فشل في المصادقة', 'error');
            }
        };

        window.testFirestore = async function() {
            log('📊 اختبار قاعدة البيانات...', 'info');
            
            if (!currentUser) {
                log('⚠️ يجب تسجيل الدخول أولاً', 'warning');
                return;
            }

            try {
                // Test write
                const testData = {
                    message: 'اختبار من النسور الماسية',
                    timestamp: Date.now(),
                    userId: currentUser.uid
                };

                const docRef = doc(db, 'test', 'test_document');
                await setDoc(docRef, testData);
                log('✅ تم كتابة البيانات بنجاح', 'success');

                // Test read
                const docSnap = await getDoc(docRef);
                if (docSnap.exists()) {
                    const data = docSnap.data();
                    log('✅ تم قراءة البيانات بنجاح', 'success');
                    log('📄 البيانات: ' + data.message, 'info');
                    updateStatus('قاعدة البيانات تعمل بشكل مثالي', 'success');
                } else {
                    log('❌ لم يتم العثور على البيانات', 'error');
                }

            } catch (error) {
                log('❌ خطأ في قاعدة البيانات: ' + error.message, 'error');
                updateStatus('خطأ في قاعدة البيانات', 'error');
            }
        };

        window.clearLog = function() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        };

        // Auto test on load
        setTimeout(() => {
            testConnection();
        }, 1000);
    </script>
</body>
</html>
